from datetime import datetime, timezone


def format_time_ago(timestamp):
    """
    Convert a timestamp to a human-readable "time ago" string.
    
    Args:
        timestamp (int): Unix timestamp
        
    Returns:
        str: Human-readable time ago string (e.g., "2 hours ago", "3 days ago")
    """
    if not timestamp:
        return "Unknown time"
    
    try:
        now = datetime.now(timezone.utc)
        past = datetime.fromtimestamp(timestamp, timezone.utc)
        diff = now - past
        
        if diff.days > 0:
            if diff.days == 1:
                return "1 day ago"
            elif diff.days < 7:
                return f"{diff.days} days ago"
            elif diff.days < 30:
                weeks = diff.days // 7
                return f"{weeks} week{'s' if weeks > 1 else ''} ago"
            else:
                months = diff.days // 30
                return f"{months} month{'s' if months > 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours > 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
        else:
            return "Just now"
    except:
        return "Unknown time"
