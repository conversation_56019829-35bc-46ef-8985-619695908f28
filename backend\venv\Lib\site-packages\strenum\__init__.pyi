import enum
from typing import Union, Sequence, Mapping, Any

class StrEnum(str, enum.Enum):
    def __new__(cls, value: Union[str, enum.auto], *args: Sequence[Any], **kwargs: Mapping[Any, Any]) -> StrEnum: ...
    def __str__(self) -> str: ...
    def _generate_next_value_(name: str, *_) -> str: ...

class LowercaseStrEnum(StrEnum):
    def _generate_next_value_(name: str, *_) -> str: ...

class UppercaseStrEnum(StrEnum):
    def _generate_next_value_(name: str, *_) -> str: ...

class CamelCaseStrEnum(StrEnum):
    def _generate_next_value_(name: str, *_) -> str: ...

class PascalCaseStrEnum(StrEnum):
    def _generate_next_value_(name: str, *_) -> str: ...

class KebabCaseStrEnum(StrEnum):
    def _generate_next_value_(name: str, *_) -> str: ...

class SnakeCaseStrEnum(StrEnum):
    def _generate_next_value_(name: str, *_) -> str: ...

class MacroCaseStrEnum(StrEnum):
    def _generate_next_value_(name: str, *_) -> str: ...

class CamelSnakeCaseStrEnum(StrEnum):
    def _generate_next_value_(name: str, *_) -> str: ...

class PascalSnakeCaseStrEnum(StrEnum):
    def _generate_next_value_(name: str, *_) -> str: ...

class SpongebobCaseStrEnum(StrEnum):
    def _generate_next_value_(name: str, *_) -> str: ...

class CobolCaseStrEnum(StrEnum):
    def _generate_next_value_(name: str, *_) -> str: ...

class HttpHeaderCaseStrEnum(StrEnum):
    def _generate_next_value_(name: str, *_) -> str: ...
