import React, { useEffect, useState, Suspense, lazy } from "react";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useExtensionAuth } from "@/hooks/useExtensionAuth";
import AppLayout from "@/components/AppLayout";
import AuthForm from "@/components/AuthForm";
import PasswordResetForm from "@/components/PasswordResetForm";
import ExtensionSetupWizard from "@/components/ExtensionSetupWizard";
import StatsCards from "@/components/StatsCards";
import AuthLoadingScreen from "@/components/AuthLoadingScreen";
import BackendConnectedWrapper from "@/components/BackendConnectedWrapper";
import { VintedConditionalRender } from "@/components/ConditionalRender";
import LoadingPlaceholder from "@/components/LoadingPlaceholder";
import { InlineLoader } from "@/components/ui/loader";
import { toast } from "sonner";
import { useVintedLogin } from "@/hooks/useVintedLogin";
import { useWebSocket } from "@/context/WebSocketContext";
import { useVintedStats } from "@/hooks/useVintedStats";
import { SessionDebugger } from "@/components/SessionDebugger";
import { autoRunSessionTests } from "@/utils/sessionTest";

// ✅ LAZY LOADING: Carica i componenti solo quando necessario
const LatestOrders = lazy(() => import("@/components/LatestOrders"));
const RecentOffers = lazy(() => import("@/components/RecentOffers"));
const RecentMessages = lazy(() => import("@/components/RecentMessages"));
const RecentLikes = lazy(() => import("@/components/RecentLikes"));

const Index = () => {
  const { user, loading: authLoading } = useAuth();
  const { isConnected, isExtensionOnline } = useWebSocket();
  const { vintedLogin } = useVintedLogin();

  // Debug logging for WebSocket state
  useEffect(() => {
    console.log("[Index] WebSocket state:", { isConnected, isExtensionOnline });
  }, [isConnected, isExtensionOnline]);
  const [isPasswordReset, setIsPasswordReset] = useState(false);
  const [showExtensionWizard, setShowExtensionWizard] = useState(false);
  const { stats: vintedStats, isLoading: loading } = useVintedStats();
  const [forceShowContent, setForceShowContent] = useState(false);

  // Remove force show content mechanism - no longer needed
  useEffect(() => {
    setForceShowContent(false);
  }, [authLoading]);

  // ✅ FIX: Run session tests in development mode
  useEffect(() => {
    autoRunSessionTests();
  }, []);

  // Check if this is an extension request
  const urlParams = new URLSearchParams(window.location.search);
  const isExtensionRequest = urlParams.get("extension") === "true";

  // Use extension auth hook only if it's an extension request
  const { isExtensionConnected, authSent, extensionId, shouldRedirect } =
    useExtensionAuth();

  useEffect(() => {
    // ✅ FIX: Use auth context session instead of calling getSession again
    // This prevents duplicate session calls and potential race conditions
    const checkPasswordReset = () => {
      // Check if the current session is a password recovery session
      if (
        user?.user_metadata?.iss === "supabase" ||
        window.location.hash.includes("type=recovery")
      ) {
        setIsPasswordReset(true);
      }
    };

    checkPasswordReset();

    // Listen for auth state changes to detect password reset
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === "PASSWORD_RECOVERY") {
        setIsPasswordReset(true);
      } else if (event === "SIGNED_IN" && isPasswordReset) {
        // Reset the flag after successful password reset
        setIsPasswordReset(false);
      }
    });

    return () => subscription.unsubscribe();
  }, [isPasswordReset, user]); // ✅ FIX: Add user dependency

  // Check if we should show the extension wizard
  useEffect(() => {
    if (
      user &&
      isConnected &&
      isExtensionOnline === false &&
      !isExtensionRequest
    ) {
      // Only show wizard if user is authenticated and extension is definitely offline
      setShowExtensionWizard(true);
    } else {
      setShowExtensionWizard(false);
    }
  }, [user, isConnected, isExtensionOnline, isExtensionRequest]);

  // Show success message when extension auth is completed
  useEffect(() => {
    if (isExtensionRequest && authSent && extensionId) {
      toast.success(
        "Extension connected successfully! Redirecting to dashboard..."
      );
    }
  }, [isExtensionRequest, authSent, extensionId]);

  // Show redirect message when extension is already connected
  useEffect(() => {
    if (isExtensionRequest && shouldRedirect && isExtensionOnline === true) {
      toast.success("Extension already connected! Redirecting to dashboard...");
    }
  }, [isExtensionRequest, shouldRedirect, isExtensionOnline]);

  // Show loading screen during auth verification
  if (authLoading) {
    console.log('[Index] Showing auth loading screen', { 
      authLoading, 
      user: user?.id,
      timestamp: new Date().toLocaleTimeString() 
    });
    return <AuthLoadingScreen message="Verifying your session..." />;
  }

  // Determine what content to show based on auth and extension status
  const renderMainContent = () => {
    // If user is not authenticated, show login form
    if (!user) {
      return <AuthForm />;
    }



    // If this is a password reset session, show password reset form
    if (isPasswordReset) {
      return <PasswordResetForm />;
    }

    // If this is an extension request and user is authenticated, show extension status
    if (isExtensionRequest) {
      // If should redirect, show redirect message
      if (shouldRedirect) {
        return (
          <div className="text-center space-y-6">
            <div className="max-w-md mx-auto">
              <h2 className="text-2xl font-bold mb-4">Redirecting...</h2>

              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800 text-sm font-medium">
                  ✅ Extension successfully connected!
                </p>
                <p className="text-green-700 text-xs mt-1">
                  Redirecting to dashboard in a moment...
                </p>
              </div>

              <div className="flex justify-center mt-4">
                <InlineLoader size="lg" />
              </div>
            </div>
          </div>
        );
      }

      return (
        <div className="text-center space-y-6">
          <div className="max-w-md mx-auto">
            <h2 className="text-2xl font-bold mb-4">Extension Integration</h2>

            {!extensionId ? (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-yellow-800 text-sm mb-2">
                  Waiting for extension to inject ID...
                </p>
                <div className="flex justify-center">
                  <InlineLoader size="lg" />
                </div>
              </div>
            ) : !authSent ? (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-blue-800 text-sm">
                  Extension detected. Sending authentication...
                </p>
                <div className="flex justify-center mt-2">
                  <InlineLoader size="lg" />
                </div>
              </div>
            ) : (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800 text-sm font-medium">
                  ✅ Authentication sent successfully!
                </p>
                <p className="text-green-700 text-xs mt-1">
                  Waiting for extension to connect...
                </p>
              </div>
            )}

            <div className="pt-4 border-t border-gray-200 mt-4">
              <div className="text-xs text-gray-500 space-y-1">
                <p>
                  Extension ID:{" "}
                  {extensionId
                    ? `🟢 ${extensionId.substring(0, 8)}...`
                    : "🔴 Not detected"}
                </p>
                <p>
                  WebSocket status:{" "}
                  {isExtensionOnline === true
                    ? "🟢 Online"
                    : isExtensionOnline === false
                    ? "🔴 Offline"
                    : "🟡 Unknown"}
                  {/* Debug info */}
                  <span className="ml-2 text-xs opacity-50">
                    (value: {String(isExtensionOnline)})
                  </span>
                </p>
                <p>Auth sent: {authSent ? "🟢 Yes" : "🔴 No"}</p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // If user wants to see the extension wizard or extension is offline, show wizard
    if (showExtensionWizard) {
      return (
        <ExtensionSetupWizard onSkip={() => setShowExtensionWizard(false)} />
      );
    }

    // Show dashboard content wrapped in backend connection check
    return (
      <BackendConnectedWrapper fullHeight={true}>
        <div>
          <StatsCards />

          {/* ✅ LAZY LOADING: Componenti che richiedono Vinted Login */}
          <VintedConditionalRender debug={true}>
            <Suspense fallback={<LoadingPlaceholder type="grid" className="mb-4 md:mb-6" />}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-4 md:mb-6">
                <RecentOffers />
                <RecentMessages />
                <RecentLikes />
              </div>
            </Suspense>
          </VintedConditionalRender>

          <VintedConditionalRender debug={true}>
            <Suspense fallback={<LoadingPlaceholder type="table" />}>
              <div className="grid grid-cols-1 gap-4 md:gap-6">
                <LatestOrders />
              </div>
            </Suspense>
          </VintedConditionalRender>
        </div>
      </BackendConnectedWrapper>
    );
  };

  return (
    <>
      <AppLayout
        title={isExtensionRequest ? "Extension Integration" : "Dashboard"}
      >
        {renderMainContent()}
      </AppLayout>
      <SessionDebugger />
    </>
  );
};

export default Index;
