# app/services/job_controller.py
import asyncio
import time
import uuid
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from enum import Enum

from app.services.job_registry import get_job_executor
from app.db.supabase_client import supabase
from app.models.plans import get_user_permissions, PermissionDeniedError

logger = logging.getLogger(__name__)


class JobStatus(Enum):
    """Stati possibili per un job persistente"""
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    COMPLETED = "completed"
    FAILED = "failed"


class JobType(Enum):
    """Tipi di job supportati"""
    FOLLOW_AUTOMATION = "follow_automation"
    REPOST_AUTOMATION = "repost_automation"
    MESSAGE_AUTOMATION = "message_automation"
    NOTIFICATION_SYNC = "notification_sync"


class JobController:
    def __init__(self):
        # Job attivi in memoria con stato persistente
        self.active_jobs: Dict[str, Dict[str, Any]] = {}
        # Task asyncio per ogni job
        self.job_tasks: Dict[str, asyncio.Task] = {}
        # Job states per pause/resume
        self.job_states: Dict[str, Dict[str, Any]] = {}
        # Cache per lookup veloci
        self.user_job_cache: Dict[str, Dict[str, str]] = {}  # user_id -> {job_type -> job_id}
        
    async def check_job_permissions(self, user_id: str, job_type: str) -> Dict[str, Any]:
        """
        Verifica se l'utente ha i permessi per avviare un job specifico
        e controlla i limiti giornalieri
        """
        try:
            # Ottieni i permessi dell'utente
            permissions = await get_user_permissions(user_id)
            
            # Mappa i job types alle feature nel piano
            job_feature_map = {
                "follow_automation": "START_FOLLOW_AUTOMATION",
                "repost_automation": "REPOST_ITEM", 
                "message_automation": "CREATE_AUTO_MESSAGE",
                "notification_sync": "GET_VINTED_STATS"  # Feature base per notifiche
            }
            
            feature_key = job_feature_map.get(job_type)
            if not feature_key:
                return {
                    "allowed": False,
                    "error": f"Unknown job type: {job_type}",
                    "error_code": "UNKNOWN_JOB_TYPE"
                }
            
            # Verifica se la feature è abilitata nel piano
            if not permissions["features"].get(feature_key, False):
                # Trova il piano che supporta questa feature
                from app.models.plans import PLANS
                required_plan = None
                for plan_id, plan_details in PLANS.items():
                    if plan_details.get("features", {}).get(feature_key, False):
                        required_plan = plan_details.get("name", plan_id)
                        break
                
                return {
                    "allowed": False,
                    "error": f"Your {permissions['plan_name']} plan does not include {job_type} functionality",
                    "error_code": "FEATURE_NOT_AVAILABLE",
                    "current_plan": permissions["plan_name"],
                    "required_plan": required_plan or "Premium"
                }
            
            # Verifica limiti giornalieri
            daily_limit_key = f"max_{feature_key}_per_day"
            if job_type == "follow_automation":
                daily_limit_key = "max_FOLLOW_AUTOMATION_actions_per_day"
            elif job_type == "repost_automation":
                daily_limit_key = "max_REPOST_ITEM_per_day"
            elif job_type == "message_automation":
                daily_limit_key = "max_CREATE_AUTO_MESSAGE_per_day"
            
            daily_limit = permissions["limits"].get(daily_limit_key, 100)
            
            # Se il limite è -1, significa illimitato
            if daily_limit == -1:
                return {
                    "allowed": True,
                    "daily_limit": "unlimited",
                    "current_usage": 0,
                    "plan_name": permissions["plan_name"]
                }
            
            # Conta le azioni di oggi per questo tipo di job
            current_usage = await self._get_user_daily_usage(user_id, job_type)
            
            if current_usage >= daily_limit:
                return {
                    "allowed": False,
                    "error": f"Daily limit reached for {job_type}. Limit: {daily_limit}, Used: {current_usage}",
                    "error_code": "DAILY_LIMIT_EXCEEDED",
                    "daily_limit": daily_limit,
                    "current_usage": current_usage,
                    "plan_name": permissions["plan_name"]
                }
            
            return {
                "allowed": True,
                "daily_limit": daily_limit,
                "current_usage": current_usage,
                "remaining": daily_limit - current_usage,
                "plan_name": permissions["plan_name"]
            }
            
        except Exception as e:
            logger.exception(f"Error checking job permissions for user {user_id}: {e}")
            return {
                "allowed": False,
                "error": f"Error checking permissions: {str(e)}",
                "error_code": "PERMISSION_CHECK_ERROR"
            }

    async def _get_user_daily_usage(self, user_id: str, job_type: str) -> int:
        """
        Ottiene il conteggio delle azioni giornaliere per un utente e tipo di job
        """
        try:
            today = datetime.now(timezone.utc).date()
            
            # Conta i job logs di oggi per questo tipo
            result = supabase.table("job_logs").select("id", count="exact") \
                .eq("user_id", user_id) \
                .contains("metadata", {"job_type": job_type}) \
                .eq("level", "success") \
                .gte("created_at", f"{today}T00:00:00Z") \
                .lt("created_at", f"{today}T23:59:59Z").execute()
            
            return result.count or 0
            
        except Exception as e:
            logger.exception(f"Error getting daily usage for user {user_id}: {e}")
            return 0
    async def start_persistent_job(self, user_id: str, job_type: str, vinted_user_id: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Avvia un job persistente che continua fino a stop manuale.
        Supporta pause/resume e persistenza completa.
        MODIFICATO: Ora restituisce un Dict con status invece di solo job_id
        """
        try:
            # 🔐 CONTROLLO PERMESSI - Verifica permessi dell'utente prima di avviare il job
            permission_check = await self.check_job_permissions(user_id, job_type)
            if not permission_check["allowed"]:
                logger.warning(f"🚫 Permission denied for user {user_id} to start job {job_type}: {permission_check['error']}")
                return {
                    "success": False,
                    "error": permission_check["error"],
                    "error_code": permission_check.get("error_code"),
                    "current_plan": permission_check.get("current_plan"),
                    "required_plan": permission_check.get("required_plan"),
                    "daily_limit": permission_check.get("daily_limit"),
                    "current_usage": permission_check.get("current_usage")
                }
            
            logger.info(f"✅ Permission granted for user {user_id} to start job {job_type}. Plan: {permission_check['plan_name']}, Remaining: {permission_check.get('remaining', 'unlimited')}")
            
            # Controlla se esiste già un job di questo tipo per l'utente (lookup ottimizzato)
            existing_job_id = self._get_user_job_id(user_id, job_type)
            if existing_job_id:
                logger.warning(f"User {user_id} attempted to start duplicate job of type {job_type}")
                return {
                    "success": False,
                    "error": f"A {job_type} job is already running",
                    "error_code": "JOB_ALREADY_RUNNING",
                    "existing_job_id": existing_job_id
                }
            
            # Genera job_id univoco
            job_id = str(uuid.uuid4())
            
            # Configurazione di default
            default_config = {
                "enabled": True,
                "interval_minutes": 5,
                "max_actions_per_day": permission_check.get("daily_limit", 100),
                "auto_pause_on_error": True,
                "retry_on_failure": True,
                "max_retries": 3
            }
            
            # Merge con config fornito
            final_config = {**default_config, **(config or {})}
            
            # Assicurati che il limite giornaliero rispetti quello del piano
            if permission_check.get("daily_limit") != "unlimited":
                final_config["max_actions_per_day"] = min(
                    final_config["max_actions_per_day"], 
                    permission_check["daily_limit"]
                )
            
            # Crea record nel database con configurazione completa
            result = supabase.table("active_jobs").insert({
                "id": job_id,
                "user_id": user_id,
                "job_type": job_type,
                "vinted_user_id": vinted_user_id,
                "config": final_config,
                "permission_info": {
                    "plan_name": permission_check["plan_name"],
                    "daily_limit": permission_check.get("daily_limit"),
                    "remaining": permission_check.get("remaining")
                },
                "created_at": datetime.now(timezone.utc).isoformat(),
                "last_activity": datetime.now(timezone.utc).isoformat(),
                "status": JobStatus.RUNNING.value
            }).execute()
            
            if not result.data:
                logger.error(f"Failed to create job record in database for job {job_id}")
                return {
                    "success": False,
                    "error": "Failed to create job record in database",
                    "error_code": "DATABASE_ERROR"
                }
            
            # Ottieni la funzione executor dalla registry
            try:
                job_executor = get_job_executor(job_type)
            except ValueError as e:
                logger.error(f"Failed to start job: {e}")
                # Rimuovi record dal DB
                supabase.table("active_jobs").delete().eq("id", job_id).execute()
                return {
                    "success": False,
                    "error": f"Job executor not found: {str(e)}",
                    "error_code": "EXECUTOR_NOT_FOUND"
                }
            
            # Stato iniziale del job
            job_state = {
                "job_id": job_id,
                "user_id": user_id,
                "job_type": job_type,
                "vinted_user_id": vinted_user_id,
                "config": final_config,
                "status": JobStatus.RUNNING.value,
                "started_at": time.time(),
                "last_activity": time.time(),
                "actions_today": permission_check.get("current_usage", 0),
                "last_action_date": datetime.now().date().isoformat(),
                "error_count": 0,
                "retry_count": 0,
                "is_paused": False,
                "pause_reason": None,
                "permission_info": {
                    "plan_name": permission_check["plan_name"],
                    "daily_limit": permission_check.get("daily_limit"),
                    "remaining": permission_check.get("remaining")
                },
                "progress": {
                    "current_phase": "initializing",
                    "total_phases": 1,
                    "phase_description": "Job started"
                }
            }
            
            # Registra in memoria con cache ottimizzata
            self.active_jobs[job_id] = job_state
            self.job_states[job_id] = job_state
            self._update_user_job_cache(user_id, job_type, job_id)
            
            # Avvia il task asincrono persistente
            task = asyncio.create_task(
                self._execute_persistent_job(job_id, user_id, job_type, vinted_user_id, job_executor, final_config)
            )
            self.job_tasks[job_id] = task
            
            # Log iniziale con informazioni sui permessi
            await self.add_job_log(job_id, f"Persistent job {job_type} started successfully (Plan: {permission_check['plan_name']})", "info", {
                "config": final_config, 
                "permission_info": job_state["permission_info"]
            })
            
            logger.info(f"✅ Started persistent job {job_id} of type {job_type} for user {user_id}")
            
            return {
                "success": True,
                "job_id": job_id,
                "job_type": job_type,
                "config": final_config,
                "permission_info": job_state["permission_info"]
            }
            
        except Exception as e:
            logger.exception(f"Error starting persistent job for user {user_id}: {e}")
            return {
                "success": False,
                "error": f"Internal error starting job: {str(e)}",
                "error_code": "INTERNAL_ERROR"
            }

    async def _cleanup_database_job(self, job_id: str, reason: str = "stopped"):
        """Helper per rimuovere un job dal database con logging"""
        try:
            # Log della rimozione
            await self.add_job_log(job_id, f"Job removed from database: {reason}", "info")
            
            # Rimuovi dal database
            result = supabase.table("active_jobs").delete().eq("id", job_id).execute()
            
            if hasattr(result, 'data') and result.data:
                logger.info(f"✅ Job {job_id} removed from database: {reason}")
            else:
                logger.warning(f"⚠️ Job {job_id} not found in database during cleanup")
                
        except Exception as e:
            logger.error(f"❌ Error removing job {job_id} from database: {e}")

    async def _update_database_job_status(self, job_id: str, updates: dict):
        """Helper per aggiornare lo stato di un job nel database"""
        try:
            result = supabase.table("active_jobs").update(updates).eq("id", job_id).execute()
            
            if hasattr(result, 'data') and result.data:
                logger.debug(f"✅ Job {job_id} status updated in database")
            else:
                logger.warning(f"⚠️ Job {job_id} not found in database during status update")
                
        except Exception as e:
            logger.error(f"❌ Error updating job {job_id} status in database: {e}")

    async def stop_job(self, job_id: str) -> bool:
        """Ferma un job in esecuzione"""
        try:
            if job_id not in self.job_tasks:
                logger.warning(f"Attempted to stop non-existent job: {job_id}")
                return False
                
            # Aggiorna stato
            if job_id in self.job_states:
                self.job_states[job_id]["status"] = JobStatus.STOPPED.value
                self.job_states[job_id]["stopped_at"] = time.time()
            
            # Cancella il task
            task = self.job_tasks[job_id]
            task.cancel()
            
            # Rimuovi dal database con logging migliorato
            await self._cleanup_database_job(job_id, "user stop request")
            
            # Log
            await self.add_job_log(job_id, "Job stopped by user", "info")
            
            # Cleanup memoria e cache
            self._cleanup_job(job_id)
            
            logger.info(f"✅ Stopped job {job_id}")
            return True
            
        except Exception as e:
            logger.exception(f"Error stopping job {job_id}: {e}")
            return False

    async def pause_job(self, job_id: str, reason: str = "User request") -> bool:
        """Mette in pausa un job persistente"""
        try:
            if job_id not in self.job_states:
                return False
                
            job_state = self.job_states[job_id]
            job_state["is_paused"] = True
            job_state["pause_reason"] = reason
            job_state["status"] = JobStatus.PAUSED.value
            job_state["paused_at"] = time.time()
            
            await self.add_job_log(job_id, f"Job paused: {reason}", "warning")
            logger.info(f"⏸️ Paused job {job_id}: {reason}")
            return True
            
        except Exception as e:
            logger.exception(f"Error pausing job {job_id}: {e}")
            return False

    async def resume_job(self, job_id: str) -> bool:
        """Riprende un job in pausa"""
        try:
            if job_id not in self.job_states:
                return False
                
            job_state = self.job_states[job_id]
            job_state["is_paused"] = False
            job_state["pause_reason"] = None
            job_state["status"] = JobStatus.RUNNING.value
            job_state["resumed_at"] = time.time()
            
            await self.add_job_log(job_id, "Job resumed", "info")
            logger.info(f"▶️ Resumed job {job_id}")
            return True
            
        except Exception as e:
            logger.exception(f"Error resuming job {job_id}: {e}")
            return False

    async def get_job_status(self, job_id: str, user_id: str) -> Dict[str, Any]:
        """Ottiene lo status dettagliato di un job"""
        if not self.is_job_owned_by_user(job_id, user_id):
            return {"status": "not_found", "error": "Job not found or not owned by user"}
        
        if job_id not in self.job_states:
            return {"status": "not_found", "error": "Job not in memory"}
        
        job_state = self.job_states[job_id].copy()
        job_state["duration"] = int(time.time() - job_state["started_at"])
        job_state["last_activity_ago"] = int(time.time() - job_state["last_activity"])
        
        return job_state

    async def get_user_jobs(self, user_id: str) -> List[Dict[str, Any]]:
        """Ottiene tutti i job di un utente"""
        user_jobs = []
        
        # Usa cache per lookup veloce
        user_job_ids = self.user_job_cache.get(user_id, {})
        
        for job_type, job_id in user_job_ids.items():
            if job_id in self.job_states:
                status = await self.get_job_status(job_id, user_id)
                user_jobs.append(status)
        
        return user_jobs

    async def get_jobs_by_type(self, user_id: str, job_type: str) -> List[Dict[str, Any]]:
        """Ottiene tutti i job di un tipo specifico per un utente"""
        user_jobs = await self.get_user_jobs(user_id)
        return [job for job in user_jobs if job.get("job_type") == job_type]

    def is_job_owned_by_user(self, job_id: str, user_id: str) -> bool:
        """Verifica se un job appartiene a un utente (ottimizzato)"""
        if job_id not in self.job_states:
            return False
        return self.job_states[job_id]["user_id"] == user_id
                
    async def add_job_log(self, job_id: str, message: str, log_type: str = "info", metadata: Dict[str, Any] = None) -> None:
        """Aggiunge un log e notifica il client"""
        try:
            if job_id not in self.job_states:
                logger.warning(f"Attempted to add log for non-existent job: {job_id}")
                return
            
            job_state = self.job_states[job_id]
            user_id = job_state["user_id"]
            job_type = job_state["job_type"]
            
            # Aggiorna last_activity
            job_state["last_activity"] = time.time()
            
            # Salva log nel database
            log_entry = {
                "job_id": job_id,
                "user_id": user_id,
                "level": log_type,
                "message": message,
                "metadata": {
                    "job_type": job_type,
                    "job_status": job_state.get("status"),
                    **(metadata or {})
                }
            }
            
            supabase.table("job_logs").insert(log_entry).execute()
            
            # Limita i log a 50 per job (aumentato da 40)
            result = supabase.table("job_logs").select("id") \
                .eq("job_id", job_id) \
                .order("created_at", desc=True) \
                .limit(51).execute()
                
            if result.data and len(result.data) > 50:
                oldest_to_keep = result.data[50]["id"]
                supabase.table("job_logs").delete() \
                    .eq("job_id", job_id) \
                    .lt("id", oldest_to_keep).execute()
            
            # Notifica via WebSocket
            from app.services.connection_manager import manager
            await manager.send_to_user_react(user_id, {
                "type": "JOB_LOG",
                "job_id": job_id,
                "job_type": job_type,
                "message": message,
                "log_type": log_type,
                "timestamp": int(time.time() * 1000),
                "metadata": metadata
            })
            
        except Exception as e:
            logger.exception(f"Error adding job log: {e}")

    async def get_job_logs(self, job_id: str, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Ottiene i log di un job per un utente"""
        try:
            # Verifica proprietà del job
            if not self.is_job_owned_by_user(job_id, user_id):
                return []
            
            # Ottieni log
            result = supabase.table("job_logs").select("*") \
                .eq("job_id", job_id) \
                .order("created_at", desc=False) \
                .limit(limit).execute()
            
            return result.data or []
            
        except Exception as e:
            logger.exception(f"Error getting job logs: {e}")
            return []

    async def _execute_persistent_job(self, job_id: str, user_id: str, job_type: str, 
                                    vinted_user_id: str, executor_func: callable, config: Dict[str, Any]) -> None:
        """
        Esegue un job persistente che continua fino a stop manuale.
        Supporta pause, resume e gestione errori robusta.
        """
        job_state = self.job_states[job_id]
        
        try:
            await self.add_job_log(job_id, f"Starting persistent execution of {job_type}", "info")
            
            # Loop principale del job persistente
            while job_state["status"] == JobStatus.RUNNING.value:
                try:
                    # Controlla se il job è stato fermato
                    if job_id not in self.job_states:
                        logger.info(f"Job {job_id} removed, stopping execution")
                        break
                    
                    # Controlla se il job è in pausa
                    if job_state.get("is_paused", False):
                        await self.add_job_log(job_id, f"Job paused: {job_state.get('pause_reason', 'Unknown')}", "warning")
                        await asyncio.sleep(30)  # Attendi 30 secondi prima di ricontrollare
                        continue
                    
                    # Controlla limiti giornalieri
                    if not await self._check_daily_limits(job_id, job_state, config):
                        await self.add_job_log(job_id, "Daily limit reached, pausing job", "warning")
                        await self.pause_job(job_id, "Daily limit reached")
                        await asyncio.sleep(3600)  # Attendi 1 ora
                        continue
                    
                    # Esegui una singola azione del job
                    await executor_func(job_id, user_id, vinted_user_id, config)
                    
                    # Aggiorna contatori
                    job_state["actions_today"] += 1
                    job_state["last_activity"] = time.time()
                    job_state["error_count"] = 0  # Reset error count on success
                    
                    # Aggiorna il database ogni 10 azioni o ogni 30 minuti
                    should_update_db = (
                        job_state["actions_today"] % 10 == 0 or 
                        time.time() - job_state.get("last_db_update", 0) > 1800  # 30 minuti
                    )
                    
                    if should_update_db:
                        await self._update_database_job_status(job_id, {
                            "last_activity": datetime.now(timezone.utc).isoformat(),
                            "actions_today": job_state["actions_today"],
                            "error_count": job_state["error_count"],
                            "status": job_state["status"]
                        })
                        job_state["last_db_update"] = time.time()
                    
                    # Log dell'azione
                    await self.add_job_log(job_id, f"Completed action {job_state['actions_today']}", "success")
                    
                    # Attendi intervallo configurato
                    interval = config.get("interval_minutes", 5)
                    await asyncio.sleep(interval * 60)
                    
                except asyncio.CancelledError:
                    logger.info(f"Job {job_id} cancelled")
                    break
                    
                except Exception as e:
                    error_msg = f"Error in persistent job execution: {str(e)}"
                    logger.exception(f"Error in job {job_id}: {e}")
                    
                    job_state["error_count"] += 1
                    job_state["last_activity"] = time.time()
                    
                    await self.add_job_log(job_id, error_msg, "error")
                    
                    # Gestione errori configurabile
                    if config.get("auto_pause_on_error", True) and job_state["error_count"] >= 3:
                        await self.pause_job(job_id, f"Auto-paused after {job_state['error_count']} errors")
                        await asyncio.sleep(300)  # Attendi 5 minuti prima di riprovare
                        continue
                    
                    # Retry logic
                    if config.get("retry_on_failure", True) and job_state["retry_count"] < config.get("max_retries", 3):
                        job_state["retry_count"] += 1
                        await self.add_job_log(job_id, f"Retrying... (attempt {job_state['retry_count']})", "warning")
                        await asyncio.sleep(60)  # Attendi 1 minuto prima di riprovare
                        continue
                    
                    # Se troppi errori, ferma il job
                    if job_state["error_count"] >= 10:
                        await self.add_job_log(job_id, "Too many errors, stopping job", "error")
                        break
            
            # Job completato o fermato
            if job_state["status"] == JobStatus.RUNNING.value:
                job_state["status"] = JobStatus.COMPLETED.value
                await self.add_job_log(job_id, "Job completed successfully", "success")
            
        except Exception as e:
            job_state["status"] = JobStatus.FAILED.value
            await self.add_job_log(job_id, f"Job failed: {str(e)}", "error")
            logger.exception(f"Fatal error in persistent job {job_id}: {e}")
            
        finally:
            # Cleanup finale con rimozione database
            try:
                if job_id in self.active_jobs:
                    # Determina la ragione del cleanup
                    cleanup_reason = "completed"
                    if job_state.get("status") == JobStatus.FAILED.value:
                        cleanup_reason = "failed"
                    elif job_state.get("status") == JobStatus.STOPPED.value:
                        cleanup_reason = "stopped"
                    
                    # Rimuovi dal database se necessario
                    await self._cleanup_database_job(job_id, cleanup_reason)
                    
                    # Cleanup memoria
                    self._cleanup_job(job_id)
                    
                    logger.info(f"🧹 Job {job_id} cleanup completed: {cleanup_reason}")
                    
            except Exception as cleanup_error:
                logger.error(f"❌ Error during job cleanup for {job_id}: {cleanup_error}")

    async def _check_daily_limits(self, job_id: str, job_state: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """Controlla se il job ha raggiunto i limiti giornalieri"""
        max_actions = config.get("max_actions_per_day", 100)
        current_date = datetime.now().date().isoformat()
        
        # Se limite illimitato, ritorna sempre True
        if max_actions == -1:
            return True
        
        # Reset contatore se è un nuovo giorno
        if job_state.get("last_action_date") != current_date:
            # Ricontrolla l'usage effettivo dal database per accuratezza
            user_id = job_state["user_id"]
            job_type = job_state["job_type"]
            actual_usage = await self._get_user_daily_usage(user_id, job_type)
            
            job_state["actions_today"] = actual_usage
            job_state["last_action_date"] = current_date
            
            # Log del reset giornaliero
            await self.add_job_log(job_id, f"Daily counter reset. Current usage: {actual_usage}/{max_actions}", "info")
        
        return job_state["actions_today"] < max_actions

    def _get_user_job_id(self, user_id: str, job_type: str) -> Optional[str]:
        """Ottiene l'ID di un job per utente e tipo (lookup ottimizzato)"""
        return self.user_job_cache.get(user_id, {}).get(job_type)
    
    async def get_job_health_status(self) -> dict:
        """Ottiene lo stato di salute di tutti i job attivi"""
        try:
            current_time = time.time()
            health_report = {
                "total_jobs": len(self.active_jobs),
                "healthy_jobs": 0,
                "stalled_jobs": 0,
                "error_jobs": 0,
                "paused_jobs": 0,
                "database_sync_issues": 0,
                "jobs_details": []
            }
            
            # Verifica consistenza database
            db_result = supabase.table("active_jobs").select("id").execute()
            db_job_ids = {job["id"] for job in (db_result.data or [])}
            memory_job_ids = set(self.active_jobs.keys())
            
            # Job nel database ma non in memoria
            orphaned_db_jobs = db_job_ids - memory_job_ids
            # Job in memoria ma non nel database  
            orphaned_memory_jobs = memory_job_ids - db_job_ids
            
            if orphaned_db_jobs or orphaned_memory_jobs:
                health_report["database_sync_issues"] = len(orphaned_db_jobs) + len(orphaned_memory_jobs)
                logger.warning(f"⚠️ Database sync issues detected: {len(orphaned_db_jobs)} orphaned DB jobs, {len(orphaned_memory_jobs)} orphaned memory jobs")
            
            # Analizza ogni job
            for job_id, job_state in self.active_jobs.items():
                last_activity = job_state.get("last_activity", 0)
                time_since_activity = current_time - last_activity
                error_count = job_state.get("error_count", 0)
                is_paused = job_state.get("is_paused", False)
                
                job_detail = {
                    "job_id": job_id,
                    "user_id": job_state.get("user_id"),
                    "job_type": job_state.get("job_type"),
                    "status": job_state.get("status"),
                    "last_activity_minutes_ago": round(time_since_activity / 60, 1),
                    "error_count": error_count,
                    "actions_today": job_state.get("actions_today", 0),
                    "is_paused": is_paused
                }
                
                if is_paused:
                    health_report["paused_jobs"] += 1
                    job_detail["health"] = "paused"
                elif error_count >= 5:
                    health_report["error_jobs"] += 1
                    job_detail["health"] = "error_prone"
                elif time_since_activity > 3600:  # Più di 1 ora di inattività
                    health_report["stalled_jobs"] += 1
                    job_detail["health"] = "stalled"
                else:
                    health_report["healthy_jobs"] += 1
                    job_detail["health"] = "healthy"
                
                health_report["jobs_details"].append(job_detail)
            
            return health_report
            
        except Exception as e:
            logger.exception(f"Error generating job health status: {e}")
            return {"error": str(e)}
    
    async def cleanup_orphaned_jobs(self) -> dict:
        """Rimuove job orfani dal database che non sono più in memoria"""
        try:
            # Ottieni job dal database
            db_result = supabase.table("active_jobs").select("*").execute()
            db_jobs = db_result.data or []
            
            cleanup_count = 0
            errors = []
            
            for db_job in db_jobs:
                job_id = db_job["id"]
                
                # Se il job non è in memoria, è probabilmente orfano
                if job_id not in self.active_jobs:
                    try:
                        await self._cleanup_database_job(job_id, "orphaned job cleanup")
                        cleanup_count += 1
                        logger.info(f"🧹 Cleaned up orphaned database job: {job_id}")
                        
                    except Exception as e:
                        error_msg = f"Error cleaning up job {job_id}: {e}"
                        errors.append(error_msg)
                        logger.error(error_msg)
            
            return {
                "success": True,
                "cleaned_jobs": cleanup_count,
                "errors": errors,
                "message": f"Cleaned up {cleanup_count} orphaned jobs"
            }
            
        except Exception as e:
            logger.exception(f"Error during orphaned job cleanup: {e}")
            return {"success": False, "error": str(e)}

    def _update_user_job_cache(self, user_id: str, job_type: str, job_id: str):
        """Aggiorna la cache per lookup veloci"""
        if user_id not in self.user_job_cache:
            self.user_job_cache[user_id] = {}
        self.user_job_cache[user_id][job_type] = job_id

    def _cleanup_job(self, job_id: str):
        """Pulisce i dati di un job dalla memoria"""
        if job_id in self.job_states:
            job_state = self.job_states[job_id]
            user_id = job_state["user_id"]
            job_type = job_state["job_type"]
            
            # Rimuovi dalla cache
            if user_id in self.user_job_cache:
                self.user_job_cache[user_id].pop(job_type, None)
                if not self.user_job_cache[user_id]:
                    del self.user_job_cache[user_id]
        
        # Rimuovi da tutte le strutture dati
        self.job_states.pop(job_id, None)
        self.job_tasks.pop(job_id, None)
        self.active_jobs.pop(job_id, None)

    async def restart_jobs_from_database(self):
        """Riavvia i job attivi dal database (chiamato all'avvio del server)"""
        try:
            result = supabase.table("active_jobs").select("*").execute()
            
            jobs = result.data or []
            if not jobs:
                logger.info("No active jobs to restart")
                return
                
            logger.info(f"🔄 Restarting {len(jobs)} active jobs from database")
            
            restarted_count = 0
            failed_count = 0
            
            for job in jobs:
                job_id = job["id"]
                user_id = job["user_id"]
                job_type = job["job_type"]
                vinted_user_id = job["vinted_user_id"]
                
                try:
                    # Verifica che l'executor esista
                    job_executor = get_job_executor(job_type)
                    
                    # Recupera la configurazione dal database o usa default
                    saved_config = job.get("config", {})
                    default_config = {
                        "enabled": True,
                        "interval_minutes": 5,
                        "max_actions_per_day": 100,
                        "auto_pause_on_error": True,
                        "retry_on_failure": True,
                        "max_retries": 3
                    }
                    
                    # Merge configurazione salvata con default
                    config = {**default_config, **saved_config}
                    
                    # Recupera informazioni sui permessi salvate
                    saved_permission_info = job.get("permission_info", {})
                    
                    # Verifica permessi attuali dell'utente
                    try:
                        current_permissions = await self.check_job_permissions(user_id, job_type)
                        if not current_permissions["allowed"]:
                            logger.warning(f"❌ Cannot restart job {job_id}: {current_permissions['error']}")
                            # Rimuovi dal database perché non più autorizzato
                            await self._cleanup_database_job(job_id)
                            failed_count += 1
                            continue
                    except Exception as e:
                        logger.error(f"Error checking permissions for job {job_id}: {e}")
                        # Proviamo comunque a riavviare con le info salvate
                    
                    # Avvia task persistente
                    task = asyncio.create_task(
                        self._execute_persistent_job(job_id, user_id, job_type, vinted_user_id, job_executor, config)
                    )
                    
                    # Stato completo per restart
                    job_state = {
                        "job_id": job_id,
                        "user_id": user_id,
                        "job_type": job_type,
                        "vinted_user_id": vinted_user_id,
                        "config": config,
                        "status": JobStatus.RUNNING.value,
                        "started_at": time.time(),
                        "last_activity": time.time(),
                        "actions_today": 0,  # Reset contatori giornalieri al restart
                        "last_action_date": datetime.now().date().isoformat(),
                        "error_count": 0,
                        "retry_count": 0,
                        "is_paused": False,
                        "pause_reason": None,
                        "permission_info": saved_permission_info or {
                            "plan_name": "unknown",
                            "daily_limit": "unknown",
                            "remaining": "unknown"
                        },
                        "progress": {
                            "current_phase": "restarted",
                            "total_phases": 1,
                            "phase_description": "Job restarted after server restart"
                        },
                        "restart_count": job.get("restart_count", 0) + 1,
                        "original_created_at": job.get("created_at"),
                        "last_restart_at": datetime.now(timezone.utc).isoformat()
                    }
                    
                    # Registra in memoria con cache ottimizzata
                    self.active_jobs[job_id] = job_state
                    self.job_states[job_id] = job_state
                    self.job_tasks[job_id] = task
                    self._update_user_job_cache(user_id, job_type, job_id)
                    
                    # Aggiorna il database con informazioni di restart
                    await self._update_database_job_status(job_id, {
                        "status": JobStatus.RUNNING.value,
                        "last_activity": datetime.now(timezone.utc).isoformat(),
                        "restart_count": job_state["restart_count"],
                        "last_restart_at": job_state["last_restart_at"]
                    })
                    
                    # Log del riavvio
                    await self.add_job_log(job_id, f"Job restarted after server restart (restart #{job_state['restart_count']})", "info")
                    
                    logger.info(f"✅ Restarted persistent job {job_id} ({job_type}) for user {user_id}")
                    restarted_count += 1
                    
                except ValueError as e:
                    # Executor non trovato - rimuovi dal database
                    logger.error(f"❌ Cannot restart job {job_id}: executor not found - {e}")
                    await self._cleanup_database_job(job_id)
                    failed_count += 1
                    
                except Exception as e:
                    logger.exception(f"❌ Error restarting job {job_id}: {e}")
                    failed_count += 1
            
            logger.info(f"🔄 Job restart completed: {restarted_count} success, {failed_count} failed")
                    
        except Exception as e:
            logger.exception(f"❌ Critical error restarting jobs from database: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Ottiene statistiche sui job attivi"""
        total_jobs = len(self.job_states)
        running_jobs = len([j for j in self.job_states.values() if j.get("status") == JobStatus.RUNNING.value])
        paused_jobs = len([j for j in self.job_states.values() if j.get("status") == JobStatus.PAUSED.value])
        stopped_jobs = len([j for j in self.job_states.values() if j.get("status") == JobStatus.STOPPED.value])
        
        # Statistiche per tipo
        job_types = {}
        for job_state in self.job_states.values():
            job_type = job_state["job_type"]
            if job_type not in job_types:
                job_types[job_type] = {"total": 0, "running": 0, "paused": 0}
            job_types[job_type]["total"] += 1
            if job_state.get("status") == JobStatus.RUNNING.value:
                job_types[job_type]["running"] += 1
            elif job_state.get("status") == JobStatus.PAUSED.value:
                job_types[job_type]["paused"] += 1
        
        return {
            "total_jobs": total_jobs,
            "running_jobs": running_jobs,
            "paused_jobs": paused_jobs,
            "stopped_jobs": stopped_jobs,
            "active_users": len(self.user_job_cache),
            "job_types": job_types,
            "memory_usage": {
                "job_states": len(self.job_states),
                "job_tasks": len(self.job_tasks),
                "user_cache": len(self.user_job_cache)
            }
        }


# Istanza globale
job_controller = JobController()
