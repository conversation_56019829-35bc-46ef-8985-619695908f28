# app/services/job_executors.py
import asyncio
import logging
import random
from typing import Dict, Any, List
from datetime import datetime, timezone, timedelta

from app.services.job_registry import job_registry, DEFAULT_JOB_CONFIGS, JOB_METADATA
from app.services.vinted_fetcher import perform_fetch_via_extension
from app.services.connection_manager import manager
from app.db.supabase_client import supabase

logger = logging.getLogger(__name__)


async def follow_automation_executor(job_id: str, user_id: str, vinted_user_id: str, config: Dict[str, Any]):
    """
    Executor per l'automazione follow/unfollow.
    Esegue una singola azione di follow o unfollow basata sulla configurazione.
    """
    try:
        # Ottieni informazioni Vinted
        vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
        if not vinted_user_info:
            raise Exception("Vinted user info not available")
        
        domain = manager.get_extension_data(user_id, "vinted_domain", "www.vinted.it")
        
        # Ottieni statistiche attuali
        stats = await _get_user_stats(user_id, vinted_user_id, domain)
        
        # Decidi se fare follow o unfollow
        action = await _decide_follow_action(stats, config)
        
        if action == "follow":
            await _perform_follow_action(user_id, vinted_user_id, domain, config)
        elif action == "unfollow":
            await _perform_unfollow_action(user_id, vinted_user_id, domain, config)
        else:
            logger.info(f"Job {job_id}: No action needed")
            
    except Exception as e:
        logger.error(f"Error in follow automation executor: {e}")
        raise


async def repost_automation_executor(job_id: str, user_id: str, vinted_user_id: str, config: Dict[str, Any]):
    """
    Executor per l'automazione repost.
    Trova e reposta item vecchi per aumentare la visibilità.
    """
    try:
        # Ottieni informazioni Vinted
        vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
        if not vinted_user_info:
            raise Exception("Vinted user info not available")
        
        domain = manager.get_extension_data(user_id, "vinted_domain", "www.vinted.it")
        
        # Trova item da repostare
        items_to_repost = await _find_items_to_repost(user_id, vinted_user_id, domain, config)
        
        if items_to_repost:
            # Reposta il primo item
            item = items_to_repost[0]
            await _repost_item(user_id, item["id"], domain)
            logger.info(f"Job {job_id}: Reposted item {item['id']}")
        else:
            logger.info(f"Job {job_id}: No items to repost")
            
    except Exception as e:
        logger.error(f"Error in repost automation executor: {e}")
        raise


async def message_automation_executor(job_id: str, user_id: str, vinted_user_id: str, config: Dict[str, Any]):
    """
    Executor per l'automazione messaggi.
    Risponde automaticamente ai messaggi ricevuti.
    """
    try:
        # Ottieni informazioni Vinted
        vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
        if not vinted_user_info:
            raise Exception("Vinted user info not available")
        
        domain = manager.get_extension_data(user_id, "vinted_domain", "www.vinted.it")
        
        # Trova messaggi non letti
        unread_messages = await _get_unread_messages(user_id, vinted_user_id, domain)
        
        if unread_messages and config.get("auto_reply_enabled", True):
            # Rispondi al primo messaggio
            message = unread_messages[0]
            await _send_auto_reply(user_id, message, domain, config)
            logger.info(f"Job {job_id}: Sent auto-reply to message {message['id']}")
        else:
            logger.info(f"Job {job_id}: No messages to reply to")
            
    except Exception as e:
        logger.error(f"Error in message automation executor: {e}")
        raise


async def notification_sync_executor(job_id: str, user_id: str, vinted_user_id: str, config: Dict[str, Any]):
    """
    Executor per la sincronizzazione delle notifiche.
    Sincronizza continuamente le notifiche da Vinted.
    """
    try:
        # Ottieni informazioni Vinted
        vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
        if not vinted_user_info:
            raise Exception("Vinted user info not available")
        
        domain = manager.get_extension_data(user_id, "vinted_domain", "www.vinted.it")
        
        # Sincronizza notifiche
        await _sync_notifications(user_id, vinted_user_id, domain, config)
        logger.info(f"Job {job_id}: Synced notifications")
        
    except Exception as e:
        logger.error(f"Error in notification sync executor: {e}")
        raise


async def likes_sync_executor(job_id: str, user_id: str, vinted_user_id: str, config: Dict[str, Any]):
    """
    Executor per la sincronizzazione dei likes.
    Sincronizza continuamente i likes da Vinted.
    """
    try:
        # Ottieni informazioni Vinted
        vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
        if not vinted_user_info:
            raise Exception("Vinted user info not available")
        
        domain = manager.get_extension_data(user_id, "vinted_domain", "www.vinted.it")
        
        # Sincronizza likes
        await _sync_likes(user_id, vinted_user_id, domain, config)
        logger.info(f"Job {job_id}: Synced likes")
        
    except Exception as e:
        logger.error(f"Error in likes sync executor: {e}")
        raise


# Funzioni helper per i job

async def _get_user_stats(user_id: str, vinted_user_id: str, domain: str) -> Dict[str, Any]:
    """Ottiene le statistiche dell'utente Vinted"""
    try:
        # Usa il workflow esistente per ottenere le statistiche
        from app.services.vinted_workflows import workflow_vinted_stats
        
        result = await workflow_vinted_stats(user_id, {})
        return result.get("stats", {})
    except Exception as e:
        logger.error(f"Error getting user stats: {e}")
        return {}


async def _decide_follow_action(stats: Dict[str, Any], config: Dict[str, Any]) -> str:
    """Decide se fare follow o unfollow basato sulle statistiche e configurazione"""
    followers = stats.get("followers", 0)
    following = stats.get("following", 0)
    target_followers = config.get("target_followers", 1000)
    max_following = config.get("max_following", 2000)
    follow_ratio = config.get("follow_ratio", 0.7)
    
    # Se abbiamo troppi following, unfollow
    if following > max_following:
        return "unfollow"
    
    # Se abbiamo raggiunto il target followers, unfollow
    if followers >= target_followers:
        return "unfollow"
    
    # Altrimenti, usa il ratio per decidere
    return "follow" if random.random() < follow_ratio else "unfollow"


async def _perform_follow_action(user_id: str, vinted_user_id: str, domain: str, config: Dict[str, Any]):
    """Esegue un'azione di follow"""
    try:
        # Trova utenti da seguire
        users_to_follow = await _find_users_to_follow(user_id, vinted_user_id, domain)
        
        if users_to_follow:
            user_to_follow = users_to_follow[0]
            
            # Usa il workflow esistente per il follow
            from app.services.vinted_workflows import workflow_follow_unified
            
            await workflow_follow_unified(user_id, {
                "action": "follow",
                "target_user_id": user_to_follow["id"]
            })
            
            logger.info(f"Followed user {user_to_follow['id']}")
    except Exception as e:
        logger.error(f"Error performing follow action: {e}")
        raise


async def _perform_unfollow_action(user_id: str, vinted_user_id: str, domain: str, config: Dict[str, Any]):
    """Esegue un'azione di unfollow"""
    try:
        # Trova utenti da unfolloware
        users_to_unfollow = await _find_users_to_unfollow(user_id, vinted_user_id, domain)
        
        if users_to_unfollow:
            user_to_unfollow = users_to_unfollow[0]
            
            # Usa il workflow esistente per l'unfollow
            from app.services.vinted_workflows import workflow_follow_unified
            
            await workflow_follow_unified(user_id, {
                "action": "unfollow",
                "target_user_id": user_to_unfollow["id"]
            })
            
            logger.info(f"Unfollowed user {user_to_unfollow['id']}")
    except Exception as e:
        logger.error(f"Error performing unfollow action: {e}")
        raise


async def _find_users_to_follow(user_id: str, vinted_user_id: str, domain: str) -> List[Dict[str, Any]]:
    """Trova utenti da seguire"""
    try:
        # Implementazione semplificata - in realtà dovrebbe cercare utenti attivi
        # Per ora restituisce una lista vuota
        return []
    except Exception as e:
        logger.error(f"Error finding users to follow: {e}")
        return []


async def _find_users_to_unfollow(user_id: str, vinted_user_id: str, domain: str) -> List[Dict[str, Any]]:
    """Trova utenti da unfolloware"""
    try:
        # Implementazione semplificata - in realtà dovrebbe cercare utenti inattivi
        # Per ora restituisce una lista vuota
        return []
    except Exception as e:
        logger.error(f"Error finding users to unfollow: {e}")
        return []


async def _find_items_to_repost(user_id: str, vinted_user_id: str, domain: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Trova item da repostare"""
    try:
        # Usa il workflow esistente per ottenere i prodotti
        from app.services.vinted_workflows import workflow_get_user_products
        
        result = await workflow_get_user_products(user_id, {})
        products = result.get("products", [])
        
        # Filtra per item vecchi
        min_age_hours = config.get("min_item_age_hours", 24)
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=min_age_hours)
        
        old_items = []
        for product in products:
            created_at = datetime.fromisoformat(product.get("created_at", "").replace("Z", "+00:00"))
            if created_at < cutoff_time:
                old_items.append(product)
        
        return old_items[:config.get("max_items_per_cycle", 5)]
    except Exception as e:
        logger.error(f"Error finding items to repost: {e}")
        return []


async def _repost_item(user_id: str, item_id: str, domain: str):
    """Reposta un item"""
    try:
        # Usa il workflow esistente per il repost
        from app.services.vinted_workflows import workflow_repost_item
        
        await workflow_repost_item(user_id, {"item_id": item_id})
    except Exception as e:
        logger.error(f"Error reposting item: {e}")
        raise


async def _get_unread_messages(user_id: str, vinted_user_id: str, domain: str) -> List[Dict[str, Any]]:
    """Ottiene messaggi non letti"""
    try:
        # Usa il workflow esistente per ottenere i messaggi
        from app.services.vinted_workflows import workflow_get_unread_messages
        
        result = await workflow_get_unread_messages(user_id, {})
        return result.get("messages", [])
    except Exception as e:
        logger.error(f"Error getting unread messages: {e}")
        return []


async def _send_auto_reply(user_id: str, message: Dict[str, Any], domain: str, config: Dict[str, Any]):
    """Invia una risposta automatica"""
    try:
        # Usa il workflow esistente per inviare messaggi
        from app.services.vinted_workflows import workflow_send_manual_message
        
        templates = config.get("message_templates", ["Grazie per il messaggio!"])
        template = random.choice(templates)
        
        await workflow_send_manual_message(user_id, {
            "conversation_id": message.get("conversation_id"),
            "message": template
        })
    except Exception as e:
        logger.error(f"Error sending auto reply: {e}")
        raise


async def _sync_notifications(user_id: str, vinted_user_id: str, domain: str, config: Dict[str, Any]):
    """Sincronizza le notifiche"""
    try:
        # Usa il workflow esistente per sincronizzare le notifiche
        from app.services.vinted_workflows import workflow_sync_vinted_notifications
        
        await workflow_sync_vinted_notifications(user_id, {})
    except Exception as e:
        logger.error(f"Error syncing notifications: {e}")
        raise


async def _sync_likes(user_id: str, vinted_user_id: str, domain: str, config: Dict[str, Any]):
    """Sincronizza i likes"""
    try:
        # Usa il workflow esistente per sincronizzare i likes
        from app.services.likes_workflows import workflow_sync_vinted_likes
        
        await workflow_sync_vinted_likes(user_id, {})
    except Exception as e:
        logger.error(f"Error syncing likes: {e}")
        raise


# Registrazione dei job persistenti
def register_persistent_jobs():
    """Registra tutti i job persistenti nel registry"""
    
    # Registra follow automation (con entrambi i formati)
    job_registry.register_persistent_job(
        "follow_automation",
        follow_automation_executor,
        default_config=DEFAULT_JOB_CONFIGS["follow_automation"],
        metadata=JOB_METADATA["follow_automation"]
    )
    # Alias per formato maiuscolo
    job_registry.persistent_jobs["FOLLOW_AUTOMATION"] = follow_automation_executor
    job_registry.job_configs["FOLLOW_AUTOMATION"] = DEFAULT_JOB_CONFIGS["follow_automation"]
    job_registry.job_metadata["FOLLOW_AUTOMATION"] = JOB_METADATA["follow_automation"]
    
    # Registra repost automation (con entrambi i formati)
    job_registry.register_persistent_job(
        "repost_automation",
        repost_automation_executor,
        default_config=DEFAULT_JOB_CONFIGS["repost_automation"],
        metadata=JOB_METADATA["repost_automation"]
    )
    # Alias per formato maiuscolo
    job_registry.persistent_jobs["REPOST_AUTOMATION"] = repost_automation_executor
    job_registry.job_configs["REPOST_AUTOMATION"] = DEFAULT_JOB_CONFIGS["repost_automation"]
    job_registry.job_metadata["REPOST_AUTOMATION"] = JOB_METADATA["repost_automation"]
    
    # Registra message automation (con entrambi i formati)
    job_registry.register_persistent_job(
        "message_automation",
        message_automation_executor,
        default_config=DEFAULT_JOB_CONFIGS["message_automation"],
        metadata=JOB_METADATA["message_automation"]
    )
    # Alias per formato maiuscolo
    job_registry.persistent_jobs["MESSAGE_AUTOMATION"] = message_automation_executor
    job_registry.job_configs["MESSAGE_AUTOMATION"] = DEFAULT_JOB_CONFIGS["message_automation"]
    job_registry.job_metadata["MESSAGE_AUTOMATION"] = JOB_METADATA["message_automation"]
    
    # Registra notification sync (con entrambi i formati)
    job_registry.register_persistent_job(
        "notification_sync",
        notification_sync_executor,
        default_config=DEFAULT_JOB_CONFIGS["notification_sync"],
        metadata=JOB_METADATA["notification_sync"]
    )
    # Alias per formato maiuscolo
    job_registry.persistent_jobs["NOTIFICATION_SYNC"] = notification_sync_executor
    job_registry.job_configs["NOTIFICATION_SYNC"] = DEFAULT_JOB_CONFIGS["notification_sync"]
    job_registry.job_metadata["NOTIFICATION_SYNC"] = JOB_METADATA["notification_sync"]
    
    # Registra likes sync (con entrambi i formati)
    job_registry.register_persistent_job(
        "likes_sync",
        likes_sync_executor,
        default_config=DEFAULT_JOB_CONFIGS["likes_sync"],
        metadata=JOB_METADATA["likes_sync"]
    )
    # Alias per formato maiuscolo
    job_registry.persistent_jobs["LIKES_SYNC"] = likes_sync_executor
    job_registry.job_configs["LIKES_SYNC"] = DEFAULT_JOB_CONFIGS["likes_sync"]
    job_registry.job_metadata["LIKES_SYNC"] = JOB_METADATA["likes_sync"]
    
    logger.info("✅ All persistent jobs registered (both lowercase and uppercase formats)")


# Registra i job all'import del modulo
register_persistent_jobs()




