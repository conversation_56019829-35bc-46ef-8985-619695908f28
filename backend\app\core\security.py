# app/core/security.py
from typing import Optional
from jose import jwt, J<PERSON><PERSON><PERSON><PERSON>
from fastapi import Request, HTTPException, status
from app.core.config import settings


async def get_user_id_from_token(token: Optional[str]) -> Optional[str]:
    if not token:
        return None
    try:
        payload = jwt.decode(
            token,
            settings.SUPABASE_JWT_SECRET,
            algorithms=[settings.ALGORITHM],
            audience="authenticated",
        )
        return payload.get("sub")
    except JW<PERSON>rror:
        return None


async def get_current_user_id(request: Request) -> str:
    """
    A dependency to get user ID from an Authorization header for HTTP routes.
    Extracts JWT token from Bearer authorization header and validates it.
    """
    # Assuming the JWT is passed as 'Bearer <token>'
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, 
            detail="Not authenticated"
        )

    token = auth_header.split(" ")[1]
    user_id = await get_user_id_from_token(token)

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, 
            detail="Invalid authentication credentials"
        )

    return user_id
