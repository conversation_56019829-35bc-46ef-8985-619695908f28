import {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { toast } from "sonner";

// Enhanced WebSocket connection states
export enum ConnectionState {
  DISCONNECTED = "disconnected",
  CONNECTING = "connecting", 
  CONNECTED = "connected",
  RECONNECTING = "reconnecting",
  FAILED = "failed"
}

export enum ConnectionQuality {
  EXCELLENT = "excellent",
  GOOD = "good", 
  POOR = "poor",
  OFFLINE = "offline"
}

export interface WebSocketMessage {
  type: string;
  workflow_key?: string;
  request_id?: string;
  data?: any;
  timestamp?: number;
  [key: string]: any;
}

export interface WebSocketContextType {
  connectionState: ConnectionState;
  connectionQuality: ConnectionQuality;
  isConnected: boolean;
  lastMessage: WebSocketMessage | null;
  sendMessage: (messageObject: WebSocketMessage) => Promise<boolean>;
  reconnect: () => void;
  isExtensionOnline: boolean | null;
  userPermissions: any;
  reconnectAttempts: number;
  messageQueueLength: number;
}

export const WebSocketContext = createContext<WebSocketContextType | null>(null);

export const useWebSocket = (): WebSocketContextType => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error("useWebSocket must be used within a WebSocketProvider");
  }
  return context;
};

// Resolve WS URL from env or derive from API URL
const deriveWsUrl = () => {
  // Prefer explicit WS URL
  const explicit = (import.meta as any)?.env?.VITE_WS_URL as string | undefined;
  if (explicit) {
    try {
      const u = new URL(explicit);
      // If explicit host is localhost but the app is opened via a LAN host, swap hostname to current window hostname
      const appHost = typeof window !== 'undefined' ? window.location.hostname : undefined;
      const isLocalHost = /^(localhost|127\.0\.0\.1|\[::1\])$/i.test(u.hostname);
      const appIsLocal = appHost ? /^(localhost|127\.0\.0\.1|\[::1\])$/i.test(appHost) : true;
      if (isLocalHost && appHost && !appIsLocal) {
        const port = u.port || (u.protocol === 'wss:' ? '443' : '80');
        return `${u.protocol}//${appHost}:${port}${u.pathname}${u.search}`;
      }
    } catch {}
    return explicit;
  }

  // Try to derive from API URL (e.g., http://localhost:8000/api/v1 -> ws://localhost:8000/ws)
  const apiUrl = (import.meta as any)?.env?.VITE_API_URL as string | undefined;
  if (apiUrl) {
    try {
      const u = new URL(apiUrl);
      const wsProtocol = u.protocol === "https:" ? "wss:" : "ws:";
      let host = u.host;
      // If API host is localhost but app is opened via LAN host, prefer current window host
      const appHost = typeof window !== 'undefined' ? window.location.hostname : undefined;
      const isLocalHost = /^(localhost|127\.0\.0\.1|\[::1\])$/i.test(u.hostname);
      if (isLocalHost && appHost && !/^(localhost|127\.0\.0\.1|\[::1\])$/i.test(appHost)) {
        // Keep backend port (from API URL) but swap hostname
        const port = u.port || (u.protocol === 'https:' ? '443' : '80');
        host = `${appHost}:${port}`;
      }
      return `${wsProtocol}//${host}/ws`;
    } catch {}
  }

  // As a last resort, use current window host on standard backend port 8000
  if (typeof window !== 'undefined' && window.location.hostname) {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    return `${wsProtocol}//${window.location.hostname}:8000/ws`;
  }

  // Fallback local
  return "ws://127.0.0.1:8000/ws";
};

// Constants for WebSocket management
const WEBSOCKET_CONFIG = {
  MAX_RECONNECT_DELAY: 30000,
  BASE_RECONNECT_DELAY: 1000,
  HEARTBEAT_INTERVAL: 25000,
  HEARTBEAT_TIMEOUT: 5000,
  CONNECTION_TIMEOUT: 10000,
  MAX_MESSAGE_QUEUE_SIZE: 50,
  DEBOUNCE_DELAY: 300,
  WS_URL: deriveWsUrl(),
} as const;

interface WebSocketProviderProps {
  children: React.ReactNode;
}

// Enhanced WebSocket state management with atomic operations
class WebSocketStateManager {
  private state: {
    connectionState: ConnectionState;
    connectionQuality: ConnectionQuality;
    reconnectAttempts: number;
    isExtensionOnline: boolean | null;
    userPermissions: any;
    lastMessage: WebSocketMessage | null;
    messageQueue: WebSocketMessage[];
  };
  
  private listeners: Set<(state: any) => void> = new Set();

  constructor() {
    this.state = {
      connectionState: ConnectionState.DISCONNECTED,
      connectionQuality: ConnectionQuality.OFFLINE,
      reconnectAttempts: 0,
      isExtensionOnline: null,
      userPermissions: null,
      lastMessage: null,
      messageQueue: [],
    };
  }

  // Thread-safe state updates
  updateState(updates: Partial<typeof this.state>) {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
  }

  getState() {
    return { ...this.state };
  }

  subscribe(listener: (state: any) => void) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.getState()));
  }

  // Atomic message queue operations
  enqueueMessage(message: WebSocketMessage): boolean {
    if (this.state.messageQueue.length >= WEBSOCKET_CONFIG.MAX_MESSAGE_QUEUE_SIZE) {
      console.warn("[WS StateManager] Message queue full, dropping oldest message");
      this.state.messageQueue.shift();
    }
    this.state.messageQueue.push(message);
    this.notifyListeners();
    return true;
  }

  dequeueAllMessages(): WebSocketMessage[] {
    const messages = [...this.state.messageQueue];
    this.state.messageQueue = [];
    this.notifyListeners();
    return messages;
  }

  clearQueue() {
    this.state.messageQueue = [];
    this.notifyListeners();
  }

  incrementReconnectAttempts() {
    this.state.reconnectAttempts++;
    this.notifyListeners();
  }

  resetReconnectAttempts() {
    this.state.reconnectAttempts = 0;
    this.notifyListeners();
  }
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const { user, session } = useAuth();
  
  // Centralized state management
  const stateManager = useMemo(() => new WebSocketStateManager(), []);
  const [wsState, setWsState] = useState(stateManager.getState());
  
  // JWT token state with proper synchronization
  const [jwtToken, setJwtToken] = useState<string | null>(null);

  // WebSocket connection management
  const wsRef = useRef<WebSocket | null>(null);
  const connectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isConnectingRef = useRef<boolean>(false);
  const shouldConnectRef = useRef<boolean>(true);
  const lastHeartbeatRef = useRef<number>(0);
  const isMountedRef = useRef<boolean>(true);
  const initialStateWatchdogRef = useRef<NodeJS.Timeout | null>(null);
  const diagnosticsPollRef = useRef<NodeJS.Timeout | null>(null);
  const diagnosticsInFlightRef = useRef<boolean>(false);
  
  // Debounce mechanism for connection attempts
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Subscribe to state manager updates
  useEffect(() => {
    const unsubscribe = stateManager.subscribe(setWsState);
    return () => {
      unsubscribe();
    };
  }, [stateManager]);

  // Enhanced JWT management with better error handling
  const getJwtToken = useCallback(async (): Promise<string | null> => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) throw error;
      return session?.access_token || null;
    } catch (error) {
      console.error("[WS Provider] Error getting JWT token:", error);
      return null;
    }
  }, []);

  // Initialize JWT token on auth changes
  useEffect(() => {
    const initializeToken = async () => {
      if (user) {
        let token: string | null = null;
        if (session?.access_token) {
          token = session.access_token as string;
        } else {
          token = await getJwtToken();
        }
        setJwtToken(token);
        shouldConnectRef.current = true;
      } else {
        setJwtToken(null);
        shouldConnectRef.current = false;
      }
    };

    initializeToken();
  }, [user, session, getJwtToken]);

  // Clear all timeouts utility
  const clearAllTimeouts = useCallback(() => {
    if (connectionTimeoutRef.current) {
      clearTimeout(connectionTimeoutRef.current);
      connectionTimeoutRef.current = null;
    }
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
    if (heartbeatTimeoutRef.current) {
      clearTimeout(heartbeatTimeoutRef.current);
      heartbeatTimeoutRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
      debounceTimeoutRef.current = null;
    }
  }, []);

  // Enhanced heartbeat mechanism
  const startHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }

    heartbeatIntervalRef.current = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN && isMountedRef.current) {
        lastHeartbeatRef.current = Date.now();
        const heartbeatMessage = {
          type: "HEARTBEAT",
          timestamp: Date.now(),
        };
        wsRef.current.send(JSON.stringify(heartbeatMessage));

        // Set timeout for heartbeat response
        if (heartbeatTimeoutRef.current) {
          clearTimeout(heartbeatTimeoutRef.current);
        }

        heartbeatTimeoutRef.current = setTimeout(() => {
          console.warn("[WS Provider] Heartbeat timeout - connection may be lost");
          stateManager.updateState({ connectionQuality: ConnectionQuality.POOR });
        }, WEBSOCKET_CONFIG.HEARTBEAT_TIMEOUT);
      }
    }, WEBSOCKET_CONFIG.HEARTBEAT_INTERVAL);
  }, [stateManager]);

  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
    if (heartbeatTimeoutRef.current) {
      clearTimeout(heartbeatTimeoutRef.current);
      heartbeatTimeoutRef.current = null;
    }
  }, []);

  // Bridge: mark extension online when presence is detected (content script alive)
  // This prevents the UI from staying at "Checking..." when the extension is clearly present.
  useEffect(() => {
    const onPresenceMessage = (event: MessageEvent) => {
      const data: any = event?.data;
      if (!data || typeof data !== 'object') return;
      const t = data.type as string | undefined;
      if (!t) return;
  if (
  t === 'VINDY_PRESENCE_PONG' ||
  t === 'VINDY_EXTENSION_PONG' ||
  t === 'VINDY_EXTENSION_ALIVE' ||
  t === 'VINDY_EXTENSION_ALIVE_DIRECT'
      ) {
        // Presence from the content script should be treated as authoritative.
        // Even if backend reports false, the page has a direct signal that the extension script is running.
        const prev = stateManager.getState().isExtensionOnline;
        if (prev !== true) {
          stateManager.updateState({ isExtensionOnline: true });
          console.log('[WS Provider] Extension presence detected via', t, '- forcing online');
          
          // Also trigger data refresh since extension is now available
          try {
            window.dispatchEvent(new Event('refreshData'));
            window.dispatchEvent(new CustomEvent('invalidateVintedUserCache'));
        // Notify other in-page listeners that extension is online (helps redirect logic)
        try { window.dispatchEvent(new CustomEvent('VINDY_EXTENSION_ONLINE', { detail: { source: t } })); } catch {}
          } catch {}
        }
      }
    };

    window.addEventListener('message', onPresenceMessage);

    // Listen for in-page custom events indicating the page sent auth and extension acked
    const onAuthAck = (e: Event) => {
      try {
        const prev = stateManager.getState().isExtensionOnline;
        if (prev !== true) {
          stateManager.updateState({ isExtensionOnline: true });
          console.log('[WS Provider] Received in-page auth ack/sent event - marking extension online');
        }
      } catch {}
    };

    window.addEventListener('VINDY_EXTENSION_AUTH_ACK', onAuthAck as EventListener);
    window.addEventListener('VINDY_EXTENSION_AUTH_SENT', onAuthAck as EventListener);

    // If the content script injected the meta tag, we can also treat that as a hint
    try {
      const markOnlineFromMeta = () => {
        const meta = document.querySelector('meta[name="vindy-extension-id"]');
        if (meta) {
          // Treat presence of meta tag as authoritative evidence the content script is running.
          // Force isExtensionOnline=true even if previously false to match client behavior.
          const prev = stateManager.getState().isExtensionOnline;
          if (prev !== true) {
            stateManager.updateState({ isExtensionOnline: true });
            try { console.log('[WS Provider] Found extension meta tag - forcing extension online'); } catch {}
          }
          return true;
        }
        return false;
      };

      // Initial check
      markOnlineFromMeta();

      // Observe DOM for late injection of the meta tag
      const observer = new MutationObserver(() => {
        if (markOnlineFromMeta()) {
          try { observer.disconnect(); } catch {}
        }
      });
      observer.observe(document.documentElement || document, {
        childList: true,
        subtree: true,
      });

      // Cleanup observer
      // Will also be disconnected if meta is found
      // and in the return cleanup below
      (window as any).__vindyMetaObserver = observer;
    } catch {}

    // Proactively ping extension for a short window to elicit a response
    // This helps when the extension announces before our listeners are mounted
    let pingCount = 0;
    const pingInterval = window.setInterval(() => {
      if (stateManager.getState().isExtensionOnline !== null || pingCount >= 8) {
        window.clearInterval(pingInterval);
        return;
      }
      const nonce = Math.random().toString(36).slice(2);
      try {
        window.postMessage({ type: 'VINDY_PRESENCE_PING', nonce }, '*');
        window.postMessage({ type: 'VINDY_EXTENSION_PING', nonce }, '*');
        pingCount += 1;
      } catch {}
    }, 1000);

    return () => {
      window.removeEventListener('message', onPresenceMessage);
  window.removeEventListener('VINDY_EXTENSION_AUTH_ACK', onAuthAck as EventListener);
  window.removeEventListener('VINDY_EXTENSION_AUTH_SENT', onAuthAck as EventListener);
      try { window.clearInterval(pingInterval); } catch {}
      try {
        const observer: MutationObserver | undefined = (window as any).__vindyMetaObserver;
        observer?.disconnect();
        (window as any).__vindyMetaObserver = undefined;
      } catch {}
    };
    // stateManager is stable (created once). No need to depend on wsState.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Enhanced message sending with queue and retry mechanism
  const sendMessage = useCallback(
    async (messageObject: WebSocketMessage): Promise<boolean> => {
      try {
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          console.log("📤 [WS SEND] Sending message:", {
            type: messageObject.type,
            workflow_key: messageObject.workflow_key,
            request_id: messageObject.request_id,
            timestamp: new Date().toLocaleTimeString(),
          });
          
          wsRef.current.send(JSON.stringify(messageObject));

          // Process any queued messages
          const queuedMessages = stateManager.dequeueAllMessages();
          if (queuedMessages.length > 0) {
            console.log("📤 [WS QUEUE] Sending queued messages:", queuedMessages.length);
            for (const msg of queuedMessages) {
              if (wsRef.current?.readyState === WebSocket.OPEN) {
                wsRef.current.send(JSON.stringify(msg));
              } else {
                stateManager.enqueueMessage(msg);
                break;
              }
            }
          }
          
          return true;
        } else {
          console.warn("⚠️ [WS QUEUE] WebSocket not open, queueing message:", {
            type: messageObject.type,
            readyState: wsRef.current?.readyState,
          });
          
          stateManager.enqueueMessage(messageObject);

          // Attempt to reconnect if not already trying
          if (!isConnectingRef.current && jwtToken && shouldConnectRef.current) {
            debouncedConnect();
          }

          toast.info("Connection unavailable. Message queued for retry.");
          return false;
        }
      } catch (error) {
        console.error("[WS Provider] Error sending message:", error);
        stateManager.enqueueMessage(messageObject);
        return false;
      }
    },
    [jwtToken, stateManager]
  );

  // Clean up connection
  const cleanupConnection = useCallback(() => {
    console.log("[WS Provider] Cleaning up connection");
    
    clearAllTimeouts();
    stopHeartbeat();
    if (initialStateWatchdogRef.current) {
      clearTimeout(initialStateWatchdogRef.current);
      initialStateWatchdogRef.current = null;
    }

    // Close WebSocket connection
    if (wsRef.current) {
      wsRef.current.onopen = null;
      wsRef.current.onmessage = null;
      wsRef.current.onerror = null;
      wsRef.current.onclose = null;

      if (wsRef.current.readyState === WebSocket.CONNECTING || wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.close(1000, "Cleanup");
      }
      wsRef.current = null;
    }

    // Reset states atomically
    isConnectingRef.current = false;
    stateManager.updateState({
      connectionState: ConnectionState.DISCONNECTED,
      connectionQuality: ConnectionQuality.OFFLINE,
      lastMessage: null,
      // Preserve last-known values to avoid indefinite "Checking..."
      isExtensionOnline: wsState.isExtensionOnline,
      userPermissions: wsState.userPermissions,
    });
  }, [clearAllTimeouts, stopHeartbeat, stateManager, wsState.isExtensionOnline, wsState.userPermissions]);

  // Enhanced connection function with proper error handling
  const connect = useCallback(async () => {
    if (!shouldConnectRef.current || !isMountedRef.current) {
      console.log("[WS Provider] Skipping connect - component unmounted or should not connect");
      return;
    }

    // Prevent multiple connection attempts
    if (isConnectingRef.current) {
      console.log("[WS Provider] Connection attempt already in progress");
      return;
    }

    // Check if already connected
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log("[WS Provider] Already connected");
      return;
    }

    // Get fresh token if needed
    let token = jwtToken;
    if (!token) {
      token = await getJwtToken();
      if (token && isMountedRef.current) {
        setJwtToken(token);
      }
    }

    isConnectingRef.current = true;
    stateManager.updateState({ 
      connectionState: wsState.reconnectAttempts > 0 ? ConnectionState.RECONNECTING : ConnectionState.CONNECTING 
    });

    // Clean up existing connection
    if (wsRef.current) {
      wsRef.current.close(1000, "Reconnecting");
    }

  // Always use client_type=react for backend compatibility
  const query = token ? `?token=${token}&client_type=react` : `?client_type=react`;
  const wsUrl = `${WEBSOCKET_CONFIG.WS_URL}${query}`;

    console.log(`[WS Provider] Connecting (attempt ${wsState.reconnectAttempts + 1}) to ${wsUrl}`);

    if (wsState.reconnectAttempts === 0) {
      toast.info("Connecting to Vindy backend...");
    }

    try {
      const webSocket = new WebSocket(wsUrl);
      wsRef.current = webSocket;

      // Connection timeout
      connectionTimeoutRef.current = setTimeout(() => {
        if (webSocket.readyState === WebSocket.CONNECTING) {
          console.warn("[WS Provider] Connection timeout");
          webSocket.close();
          stateManager.updateState({ connectionState: ConnectionState.FAILED });
        }
      }, WEBSOCKET_CONFIG.CONNECTION_TIMEOUT);

      webSocket.onopen = () => {
        if (!isMountedRef.current) return;

        console.log("🔗 [WS CONNECT] WebSocket connected successfully!");
        
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current);
          connectionTimeoutRef.current = null;
        }

        isConnectingRef.current = false;
        stateManager.resetReconnectAttempts();
        stateManager.updateState({
          connectionState: ConnectionState.CONNECTED,
          connectionQuality: ConnectionQuality.GOOD,
        });

        startHeartbeat();
        toast.success("Connected to Vindy backend!");

        // Watchdog for INITIAL_STATE arrival
        if (initialStateWatchdogRef.current) {
          clearTimeout(initialStateWatchdogRef.current);
        }
        // Watchdog no longer flips status to false; we keep it unknown until we get a signal
        initialStateWatchdogRef.current = setTimeout(() => {
          // If we still don't know after a few seconds, kick a diagnostics check
          const current = stateManager.getState();
          if (current.isExtensionOnline === null) {
            // Trigger a diagnostics poll to avoid UI being stuck at "Checking..."
            void pollDiagnosticsOnce();
          }
        }, 4000);

        // Proactively request initial state
        try {
          console.log("[WS Provider] Sending REQUEST_INITIAL_STATE");
          webSocket.send(JSON.stringify({ type: "REQUEST_INITIAL_STATE" }));
          
          // Also immediately request extension status to avoid delays
          webSocket.send(JSON.stringify({ type: "GET_EXTENSION_STATUS" }));
        } catch (e) {
          console.warn("[WS Provider] Failed to send initial requests", e);
        }

        // Notify data refresh
        window.dispatchEvent(new Event('refreshData'));
        window.dispatchEvent(new CustomEvent('invalidateVintedUserCache'));

        // Send queued messages
        const queuedMessages = stateManager.dequeueAllMessages();
        if (queuedMessages.length > 0) {
          console.log("📤 [WS QUEUE] Sending queued messages:", queuedMessages.length);
          queuedMessages.forEach((msg) => {
            if (webSocket.readyState === WebSocket.OPEN) {
              webSocket.send(JSON.stringify(msg));
            }
          });
        }
      };

      webSocket.onmessage = (event) => {
        if (!isMountedRef.current) return;

        try {
          const messageData: WebSocketMessage = JSON.parse(event.data);

          // Add debug logging for all messages
          console.log("[WS Provider] 📨 Received message:", {
            type: messageData.type,
            data: messageData,
            timestamp: new Date().toLocaleTimeString()
          });

          // Handle heartbeat response
          if (messageData.type === "HEARTBEAT_RESPONSE") {
            if (heartbeatTimeoutRef.current) {
              clearTimeout(heartbeatTimeoutRef.current);
              heartbeatTimeoutRef.current = null;
            }
            stateManager.updateState({ connectionQuality: ConnectionQuality.GOOD });
            return;
          }

          // Handle initial state to set permissions
          if (messageData.type === "INITIAL_STATE") {
            console.log("[WS Provider] Received INITIAL_STATE:", messageData);
            if (initialStateWatchdogRef.current) {
              clearTimeout(initialStateWatchdogRef.current);
              initialStateWatchdogRef.current = null;
            }
            stateManager.updateState({
              isExtensionOnline: messageData.is_extension_online,
              userPermissions: messageData.permissions,
            });

            console.log("[WS Provider] Extension status from INITIAL_STATE:", messageData.is_extension_online);
            console.log("[WS Provider] User permissions received:", messageData.permissions);

            if (messageData.is_extension_online === true) {
              window.dispatchEvent(new Event('refreshData'));
              window.dispatchEvent(new CustomEvent('invalidateVintedUserCache'));
            }
          }

          // Handle workflow results
          if (messageData.type === "WORKFLOW_RESULT") {
            console.log("📊 [WORKFLOW RESULT]", {
              workflow_key: messageData.workflow_key,
              status: messageData.status,
              timestamp: new Date().toLocaleTimeString(),
            });
          }

          // If we get Vinted login info updates, the extension must be online
          if (messageData.type === "VINTED_LOGIN_INFO_UPDATE") {
            const current = stateManager.getState();
            if (current.isExtensionOnline !== true) {
              stateManager.updateState({ isExtensionOnline: true });
            }
          }

          // Update last message
          stateManager.updateState({
            lastMessage: {
              ...messageData,
              timestamp: Date.now(),
            },
          });

          // Handle extension status updates
          if (messageData.type === "EXTENSION_STATUS_UPDATE") {
            console.log("[WS Provider] 🔌 Received EXTENSION_STATUS_UPDATE:", messageData);
            // Clear any pending initial-state watchdog once we have a definitive status
            if (initialStateWatchdogRef.current) {
              clearTimeout(initialStateWatchdogRef.current);
              initialStateWatchdogRef.current = null;
            }

            // Use fresh state to avoid stale closure issues
            const current = stateManager.getState();
            const wasOnline = current.isExtensionOnline;
            const newExtensionStatus = messageData.is_online as boolean;

            console.log("[WS Provider] 🔄 Extension status change:", {
              wasOnline,
              newStatus: newExtensionStatus,
              messageData
            });

            stateManager.updateState({ isExtensionOnline: newExtensionStatus });

            if (newExtensionStatus && !wasOnline) {
              console.log("[WS Provider] ✅ Extension came online - triggering data refresh");
              toast.success(
                messageData.vinted_username
                  ? `Extension connected as: ${messageData.vinted_username}`
                  : "Extension connected"
              );
              // Trigger immediate data refresh
              window.dispatchEvent(new Event('refreshData'));
              window.dispatchEvent(new CustomEvent('invalidateVintedUserCache'));
            } else if (!newExtensionStatus && wasOnline) {
              console.log("[WS Provider] ❌ Extension went offline");
              toast.info("Extension disconnected");
            }
          }
        } catch (error) {
          console.error("[WS Provider] Error parsing message:", error);
        }
      };

      webSocket.onerror = (errorEvent) => {
        if (!isMountedRef.current) return;
        console.error("[WS Provider] WebSocket error:", errorEvent);
        
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current);
          connectionTimeoutRef.current = null;
        }

        isConnectingRef.current = false;
        stateManager.updateState({ 
          connectionState: ConnectionState.FAILED,
          connectionQuality: ConnectionQuality.POOR 
        });
      };

      webSocket.onclose = (event) => {
        if (!isMountedRef.current) return;

        console.log(`[WS Provider] WebSocket closed. Code: ${event.code}, Reason: ${event.reason || "None"}`);
        
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current);
          connectionTimeoutRef.current = null;
        }
        if (initialStateWatchdogRef.current) {
          clearTimeout(initialStateWatchdogRef.current);
          initialStateWatchdogRef.current = null;
        }

        const wasConnected = wsState.connectionState === ConnectionState.CONNECTED;
        isConnectingRef.current = false;
        stopHeartbeat();
        
        stateManager.updateState({
          connectionState: ConnectionState.DISCONNECTED,
          connectionQuality: ConnectionQuality.OFFLINE,
          // Preserve last known states to keep UI informative during reconnects
          isExtensionOnline: wsState.isExtensionOnline,
          userPermissions: wsState.userPermissions,
        });

        if (wasConnected && event.code !== 1000) {
          toast.info("Disconnected from Vindy backend");
        }

  // Auto-reconnect logic with exponential backoff
  // Do NOT gate on current token; connect() will fetch a fresh one if needed
  if (shouldConnectRef.current && event.code !== 1008 && event.code !== 1000) {
          scheduleReconnect();
        } else if (event.code === 1000) {
          stateManager.resetReconnectAttempts();
        }
      };
    } catch (error) {
      console.error("[WS Provider] Error creating WebSocket:", error);
      isConnectingRef.current = false;
      stateManager.updateState({ connectionState: ConnectionState.FAILED });
      toast.error("Failed to connect to backend");

      if (shouldConnectRef.current) {
        scheduleReconnect();
      }
    }
  }, [jwtToken, wsState, getJwtToken, startHeartbeat, stopHeartbeat, stateManager]);

  // Diagnostics fallback: query backend for current WS/extension status when unknown
  const pollDiagnosticsOnce = useCallback(async () => {
    if (diagnosticsInFlightRef.current) return;
    if (!jwtToken) return;
    diagnosticsInFlightRef.current = true;
    console.log("[WS Provider] Polling diagnostics for extension status...");
    try {
      const apiBase = (import.meta as any)?.env?.VITE_API_URL as string | undefined;
      if (!apiBase) {
        console.warn("[WS Provider] No VITE_API_URL configured");
        return;
      }
      const url = new URL(apiBase);
      // Ensure we hit the same host/port as API base
      const diagUrl = `${url.origin}/api/v1/diagnostics/ws-status`;
      console.log("[WS Provider] Fetching diagnostics from:", diagUrl);
      const res = await fetch(diagUrl, {
        method: "GET",
        headers: { Authorization: `Bearer ${jwtToken}` },
      });
      if (!res.ok) {
        console.warn("[WS Provider] Diagnostics request failed:", res.status, res.statusText);
        return;
      }
      const data = await res.json();
      console.log("[WS Provider] Diagnostics response:", data);
      if (typeof data?.extension_online === "boolean") {
        console.log("[WS Provider] Setting extension status from diagnostics:", data.extension_online);
        stateManager.updateState({ isExtensionOnline: data.extension_online });
      }
    } catch (e) {
      console.warn("[WS Provider] Diagnostics poll failed", e);
    } finally {
      diagnosticsInFlightRef.current = false;
    }
  }, [jwtToken, stateManager]);

  // When connected and status unknown, kick periodic diagnostics until known
  useEffect(() => {
    const connected = wsState.connectionState === ConnectionState.CONNECTED;
    if (connected && wsState.isExtensionOnline === null && jwtToken) {
      // Do an immediate check and then a short-lived retry loop
      void pollDiagnosticsOnce();

      if (diagnosticsPollRef.current) clearInterval(diagnosticsPollRef.current);
      diagnosticsPollRef.current = setInterval(() => {
        const current = stateManager.getState();
        if (current.isExtensionOnline !== null) {
          if (diagnosticsPollRef.current) {
            clearInterval(diagnosticsPollRef.current);
            diagnosticsPollRef.current = null;
          }
          return;
        }
        void pollDiagnosticsOnce();
      }, 3000);
    }

    return () => {
      if (diagnosticsPollRef.current) {
        clearInterval(diagnosticsPollRef.current);
        diagnosticsPollRef.current = null;
      }
    };
  }, [wsState.connectionState, wsState.isExtensionOnline, jwtToken, pollDiagnosticsOnce, stateManager]);

  // Schedule reconnection with exponential backoff
  const scheduleReconnect = useCallback(() => {
    if (!shouldConnectRef.current || !isMountedRef.current) return;

    // Increment first, then compute using the latest value to avoid stale closures
    stateManager.incrementReconnectAttempts();
    const attempts = stateManager.getState().reconnectAttempts;
    const delay = Math.min(
      WEBSOCKET_CONFIG.BASE_RECONNECT_DELAY * Math.pow(2, attempts),
      WEBSOCKET_CONFIG.MAX_RECONNECT_DELAY
    );

    console.log(`[WS Provider] Scheduling reconnect in ${delay / 1000}s...`);

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

  reconnectTimeoutRef.current = setTimeout(() => {
      if (shouldConnectRef.current && isMountedRef.current) {
        connect();
      }
    }, delay);
  }, [connect, stateManager, wsState.reconnectAttempts]);

  // Debounced connect function to prevent multiple rapid connection attempts
  const debouncedConnect = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      connect();
    }, WEBSOCKET_CONFIG.DEBOUNCE_DELAY);
  }, [connect]);

  // Manual reconnect function
  const reconnect = useCallback(() => {
    console.log("[WS Provider] Manual reconnect requested");
    stateManager.resetReconnectAttempts();

    if (wsRef.current) {
      wsRef.current.close(1000, "Manual reconnect");
    }

    if (isMountedRef.current) {
      debouncedConnect();
    }
  }, [debouncedConnect, stateManager]);

  // Main connection effect - triggers on token change
  useEffect(() => {
    if (jwtToken && shouldConnectRef.current) {
      if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
        debouncedConnect();
      }
    } else if (!jwtToken) {
      cleanupConnection();
      stateManager.clearQueue();
    }
  }, [jwtToken, debouncedConnect, cleanupConnection, stateManager]);

  // Page visibility effect - reconnect when page becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && jwtToken && shouldConnectRef.current) {
        console.log("[WS Provider] Page visible, checking connection...");
        if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
          if (isMountedRef.current && shouldConnectRef.current) {
            debouncedConnect();
          }
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => document.removeEventListener("visibilitychange", handleVisibilityChange);
  }, [jwtToken, debouncedConnect]);

  // Focus event - reconnect when window gains focus  
  useEffect(() => {
    const handleFocus = () => {
      if (jwtToken && shouldConnectRef.current) {
        console.log("[WS Provider] Window focused, checking connection...");
        if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
          if (isMountedRef.current && shouldConnectRef.current) {
            debouncedConnect();
          }
        }
      }
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [jwtToken, debouncedConnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log("[WS Provider] Component unmounting, cleaning up");
      isMountedRef.current = false;
      shouldConnectRef.current = false;
      cleanupConnection();
      stateManager.clearQueue();
    };
  }, [cleanupConnection, stateManager]);

  // Memoized context value to prevent unnecessary re-renders
  const contextValue = useMemo<WebSocketContextType>(() => ({
    connectionState: wsState.connectionState,
    connectionQuality: wsState.connectionQuality,
    isConnected: wsState.connectionState === ConnectionState.CONNECTED,
    lastMessage: wsState.lastMessage,
    sendMessage,
    reconnect,
    isExtensionOnline: wsState.isExtensionOnline,
    userPermissions: wsState.userPermissions,
    reconnectAttempts: wsState.reconnectAttempts,
    messageQueueLength: wsState.messageQueue.length,
  }), [wsState, sendMessage, reconnect]);

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
};
