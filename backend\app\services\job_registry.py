# app/services/job_registry.py
from typing import Dict, Callable, Awaitable, Any, Optional
import logging
from enum import Enum

logger = logging.getLogger(__name__)

# Tipo per le funzioni di job persistenti
PersistentJobExecutorFunc = Callable[[str, str, str, Dict[str, Any]], Awaitable[None]]

# Tipo per le funzioni di job istantanei (per compatibilità)
InstantJobExecutorFunc = Callable[[str, str, str], Awaitable[None]]


class JobType(Enum):
    """Tipi di job supportati"""
    FOLLOW_AUTOMATION = "follow_automation"
    REPOST_AUTOMATION = "repost_automation"
    MESSAGE_AUTOMATION = "message_automation"
    NOTIFICATION_SYNC = "notification_sync"
    LIKES_SYNC = "likes_sync"


class JobRegistry:
    """
    Registry unificato per tutti i tipi di job (istantanei e persistenti).
    Supporta configurazione, validazione e metadati per ogni job.
    """
    
    def __init__(self):
        # Job persistenti (che continuano fino a stop manuale)
        self.persistent_jobs: Dict[str, PersistentJobExecutorFunc] = {}
        # Job istantanei (che terminano automaticamente)
        self.instant_jobs: Dict[str, InstantJobExecutorFunc] = {}
        # Configurazioni di default per job persistenti
        self.job_configs: Dict[str, Dict[str, Any]] = {}
        # Validatori per configurazioni
        self.job_validators: Dict[str, Callable] = {}
        # Metadati per ogni job
        self.job_metadata: Dict[str, Dict[str, Any]] = {}
    
    def register_persistent_job(
        self, 
        job_type: str, 
        executor: PersistentJobExecutorFunc,
        default_config: Dict[str, Any] = None,
        validator: Callable = None,
        metadata: Dict[str, Any] = None
    ):
        """
        Registra un job persistente che continua fino a stop manuale.
        
        Args:
            job_type: Tipo del job (es. "follow_automation")
            executor: Funzione che esegue il job
            default_config: Configurazione di default
            validator: Funzione per validare la configurazione
            metadata: Metadati del job (nome, descrizione, etc.)
        """
        self.persistent_jobs[job_type] = executor
        self.job_configs[job_type] = default_config or {}
        self.job_validators[job_type] = validator
        self.job_metadata[job_type] = metadata or {}
        
        logger.info(f"✅ Registered persistent job '{job_type}'")
    
    def register_instant_job(self, job_type: str, executor: InstantJobExecutorFunc):
        """
        Registra un job istantaneo che termina automaticamente.
        Mantenuto per compatibilità con il sistema esistente.
        """
        self.instant_jobs[job_type] = executor
        logger.info(f"✅ Registered instant job '{job_type}'")
    
    def get_job_executor(self, job_type: str) -> PersistentJobExecutorFunc:
        """
        Ottiene la funzione executor per un tipo di job.
        Supporta sia job persistenti che istantanei e formati maiuscolo/minuscolo.
        """
        # Normalizza il job_type (supporta sia maiuscolo che minuscolo)
        normalized_job_type = job_type.lower()
        
        # Prima controlla job persistenti con formato normalizzato
        if normalized_job_type in self.persistent_jobs:
            return self.persistent_jobs[normalized_job_type]
        
        # Controlla anche il formato originale (per retrocompatibilità)
        if job_type in self.persistent_jobs:
            return self.persistent_jobs[job_type]
        
        # Poi controlla job istantanei (per compatibilità)
        if normalized_job_type in self.instant_jobs:
            # Wrapper per convertire job istantaneo in persistente
            instant_executor = self.instant_jobs[normalized_job_type]
            
            async def persistent_wrapper(job_id: str, user_id: str, vinted_user_id: str, config: Dict[str, Any]):
                await instant_executor(job_id, user_id, vinted_user_id)
            
            return persistent_wrapper
        
        # Controlla anche formato originale per instant jobs
        if job_type in self.instant_jobs:
            instant_executor = self.instant_jobs[job_type]
            
            async def persistent_wrapper(job_id: str, user_id: str, vinted_user_id: str, config: Dict[str, Any]):
                await instant_executor(job_id, user_id, vinted_user_id)
            
            return persistent_wrapper
        
        raise ValueError(f"Job type '{job_type}' is not supported or not a persistent job. Available types: {list(self.persistent_jobs.keys()) + list(self.instant_jobs.keys())}")
    
    def get_job_config(self, job_type: str) -> Dict[str, Any]:
        """Ottiene la configurazione di default per un job"""
        return self.job_configs.get(job_type, {})
    
    def validate_job_config(self, job_type: str, config: Dict[str, Any]) -> bool:
        """Valida la configurazione di un job"""
        validator = self.job_validators.get(job_type)
        if validator:
            return validator(config)
        return True
    
    def get_job_metadata(self, job_type: str) -> Dict[str, Any]:
        """Ottiene i metadati di un job"""
        return self.job_metadata.get(job_type, {})
    
    def is_persistent_job(self, job_type: str) -> bool:
        """Verifica se un job è persistente"""
        return job_type in self.persistent_jobs
    
    def is_instant_job(self, job_type: str) -> bool:
        """Verifica se un job è istantaneo"""
        return job_type in self.instant_jobs
    
    def get_registered_job_types(self) -> Dict[str, Dict[str, Any]]:
        """Restituisce tutti i tipi di job registrati con metadati"""
        all_jobs = {}
        
        # Job persistenti
        for job_type in self.persistent_jobs:
            all_jobs[job_type] = {
                "type": "persistent",
                "metadata": self.get_job_metadata(job_type),
                "default_config": self.get_job_config(job_type)
            }
        
        # Job istantanei
        for job_type in self.instant_jobs:
            all_jobs[job_type] = {
                "type": "instant",
                "metadata": self.get_job_metadata(job_type),
                "default_config": {}
            }
        
        return all_jobs


# Istanza globale del registry
job_registry = JobRegistry()


# Funzioni di compatibilità per il sistema esistente
def register_job_executor(job_type: str):
    """
    Decoratore per registrare una funzione come executor di un job istantaneo.
    Mantenuto per compatibilità.
    """
    def decorator(func: InstantJobExecutorFunc) -> InstantJobExecutorFunc:
        job_registry.register_instant_job(job_type, func)
        return func
    return decorator


def get_job_executor(job_type: str) -> PersistentJobExecutorFunc:
    """
    Ottiene la funzione executor per un tipo di job.
    Wrapper per la compatibilità con il sistema esistente.
    """
    return job_registry.get_job_executor(job_type)


def get_registered_job_types() -> list:
    """
    Restituisce tutti i tipi di job registrati.
    Mantenuto per compatibilità.
    """
    return list(job_registry.get_registered_job_types().keys())


# Configurazioni di default per job persistenti comuni
DEFAULT_JOB_CONFIGS = {
    "follow_automation": {
        "enabled": True,
        "interval_minutes": 5,
        "max_actions_per_day": 100,
        "auto_pause_on_error": True,
        "retry_on_failure": True,
        "max_retries": 3,
        "follow_ratio": 0.7,  # 70% follow, 30% unfollow
        "target_followers": 1000,
        "max_following": 2000
    },
    "repost_automation": {
        "enabled": True,
        "interval_minutes": 60,  # Ogni ora
        "max_actions_per_day": 10,
        "auto_pause_on_error": True,
        "retry_on_failure": True,
        "max_retries": 3,
        "repost_old_items": True,
        "min_item_age_hours": 24,
        "max_items_per_cycle": 5
    },
    "message_automation": {
        "enabled": True,
        "interval_minutes": 30,
        "max_actions_per_day": 50,
        "auto_pause_on_error": True,
        "retry_on_failure": True,
        "max_retries": 3,
        "auto_reply_enabled": True,
        "message_templates": [],
        "response_delay_minutes": 5
    },
    "notification_sync": {
        "enabled": True,
        "interval_minutes": 10,
        "max_actions_per_day": 1000,
        "auto_pause_on_error": False,
        "retry_on_failure": True,
        "max_retries": 5,
        "sync_likes": True,
        "sync_messages": True,
        "sync_offers": True
    },
    "likes_sync": {
        "enabled": True,
        "interval_minutes": 15,
        "max_actions_per_day": 500,
        "auto_pause_on_error": False,
        "retry_on_failure": True,
        "max_retries": 3,
        "sync_new_likes": True,
        "update_existing": False
    }
}


# Metadati per i job
JOB_METADATA = {
    "follow_automation": {
        "name": "Follow Automation",
        "description": "Automatically follows and unfollows users based on configured rules",
        "category": "social",
        "icon": "users",
        "color": "blue",
        "requires_extension": True,
        "permissions": ["FOLLOW_USER", "UNFOLLOW_USER"]
    },
    "repost_automation": {
        "name": "Repost Automation", 
        "description": "Automatically reposts old items to increase visibility",
        "category": "content",
        "icon": "refresh-cw",
        "color": "green",
        "requires_extension": True,
        "permissions": ["REPOST_ITEM"]
    },
    "message_automation": {
        "name": "Message Automation",
        "description": "Automatically responds to messages and sends follow-up messages",
        "category": "communication", 
        "icon": "message-circle",
        "color": "purple",
        "requires_extension": True,
        "permissions": ["SEND_MANUAL_MESSAGE"]
    },
    "notification_sync": {
        "name": "Notification Sync",
        "description": "Continuously syncs notifications from Vinted",
        "category": "sync",
        "icon": "bell",
        "color": "orange",
        "requires_extension": True,
        "permissions": ["GET_UNREAD_MESSAGES"]
    },
    "likes_sync": {
        "name": "Likes Sync",
        "description": "Continuously syncs likes from Vinted notifications",
        "category": "sync",
        "icon": "heart",
        "color": "red",
        "requires_extension": True,
        "permissions": ["GET_RECENT_LIKES"]
    }
}
