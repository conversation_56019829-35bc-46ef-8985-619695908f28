# app/core/config.py
import os
import logging
import stripe  # <--- 1. IMPORT THE STRIPE LIBRARY
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Carica le variabili dal file .env
load_dotenv(override=True)


class Settings(BaseSettings):
    APP_TITLE: str = "Vindy Backend - Product-Based Permissions"
    APP_VERSION: str = "0.8.0"

    # Supabase Credentials
    SUPABASE_URL: str
    SUPABASE_SERVICE_ROLE_KEY: str
    SUPABASE_JWT_SECRET: str

    # JWT Settings
    ALGORITHM: str = "HS256"

    # Application settings
    FETCH_TIMEOUT: float = 90.0
    FRONTEND_URL: str = "http://localhost:8080"  # Default value

    # Stripe
    STRIPE_API_KEY: str
    STRIPE_WEBHOOK_SECRET: str
    # Optional: price IDs for live mode (set these for production)
    STRIPE_PRICE_ESSENTIALS: str | None = None
    STRIPE_PRICE_ESSENTIALS_PLUS: str | None = None

    class Config:
        case_sensitive = True


settings = Settings()

# --- THIS IS THE FIX ---
# 2. CONFIGURE THE STRIPE LIBRARY WITH THE API KEY FROM YOUR SETTINGS
if settings.STRIPE_API_KEY:
    # Trim whitespace to avoid 'Invalid API Key' due to stray spaces/newlines
    _api_key = settings.STRIPE_API_KEY.strip()
    stripe.api_key = _api_key
    try:
        _mode = "live" if _api_key.startswith("sk_live") else "test"
        logging.info(f"Stripe configured (mode={_mode})")
    except Exception:
        pass
else:
    logging.warning(
        "ATTENZIONE: STRIPE_API_KEY non è impostata! Le chiamate a Stripe falliranno."
    )
# ------------------------

# Build a mapping of Stripe product_id -> internal plan keyword using configured price IDs
STRIPE_PRODUCT_TO_PLAN: dict = {}
try:
    price_map = {
        "essentials": settings.STRIPE_PRICE_ESSENTIALS,
        "essentials_plus": settings.STRIPE_PRICE_ESSENTIALS_PLUS,
    }
    for plan_key, pid in price_map.items():
        if pid:
            try:
                pr = stripe.Price.retrieve(pid)
                product_id = pr.get("product")
                if product_id:
                    STRIPE_PRODUCT_TO_PLAN[product_id] = plan_key
            except Exception:
                logging.debug(f"Could not retrieve Stripe price {pid} for mapping")
except Exception:
    # Be quiet if stripe isn't configured yet
    STRIPE_PRODUCT_TO_PLAN = {}

# Note: keep STRIPE_PRODUCT_TO_PLAN as a module-level variable (do not attach to pydantic Settings())


# Create logger (logging configuration is done in main.py)
logger = logging.getLogger(__name__)

# Validate essential settings
if not settings.SUPABASE_JWT_SECRET:
    logger.error("ATTENZIONE: SUPABASE_JWT_SECRET non è impostata!")
if not settings.SUPABASE_URL or not settings.SUPABASE_SERVICE_ROLE_KEY:
    logger.error(
        "ATTENZIONE: DATABASE_URL o SUPABASE_SERVICE_ROLE_KEY non sono impostate!"
    )
