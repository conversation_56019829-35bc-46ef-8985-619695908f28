# app/services/vinted_fetcher.py
import asyncio
import uuid
from typing import Dict, Any
from app.core.config import settings
from app.services.connection_manager import manager

# This state must be managed here alongside the function that uses it
jobs_in_progress: Dict[str, asyncio.Future] = {}


async def perform_fetch_via_extension(
    user_id: str, fetch_details: dict
) -> Dict[str, Any]:
    if not manager.is_extension_online(user_id):
        raise ConnectionError(f"Extension for {user_id} is not online.")

    job_id = str(uuid.uuid4())
    future = asyncio.get_running_loop().create_future()
    jobs_in_progress[job_id] = future

    try:
        await manager.send_to_extension(
            user_id,
            {
                "job_id": job_id,
                "task": fetch_details.get("job_type", "FETCH_RAW_URL"),
                "details": fetch_details,
            },
        )
        job_result = await asyncio.wait_for(future, timeout=settings.FETCH_TIMEOUT)

        if job_result.get("status") == "success":
            return job_result
        else:
            raise RuntimeError(
                f"Job {job_id} failed: {job_result.get('error', 'Error from extension')}"
            )
    except asyncio.TimeoutError:
        raise TimeoutError(f"Timeout for Job {job_id}.")
    finally:
        jobs_in_progress.pop(job_id, None)


def get_active_domain(user_id: str, params: dict) -> str:
    return manager.get_extension_data(
        user_id, "vinted_domain", params.get("domain", "www.vinted.it")
    )
