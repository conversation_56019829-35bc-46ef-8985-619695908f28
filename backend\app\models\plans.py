# app/models/plans.py
import logging
from datetime import datetime, timezone, date
from typing import Dict, Any
from app.db.supabase_client import supabase
from app.core.config import settings

logger = logging.getLogger(__name__)


class PermissionDeniedError(Exception):
    pass


PLANS = {
    "free": {
        "name": "Free",
        "keyword": "free",
        "features": {
            "GET_USER_PROFILE": True,
            "GET_PLAN_AND_USAGE": True,
            "GET_USER_PRODUCTS": True,
            "CHECK_VINTED_LOGIN_STATUS": True,
            "GET_VINTED_STATS": True,
            "GET_VINTED_OFFERS": True,  # Abilitato per test
            "GET_VINTED_ORDERS": True,  # Abilitato per tutti i piani
            "GET_UNREAD_MESSAGES": True,  # Abilitato per tutti i piani
            "ADVANCED_ANALYTICS": False,
            "ACCEPT_VINTED_OFFER": True,  # Abilitato per test
            "DECLINE_VINTED_OFFER": True,  # Abilitato per test
            "COUNTER_VINTED_OFFER": True,  # Abilitato per test
            "GET_FOLLOWER_STATS": False,
            "REPOST_ITEM": True,  # Accessibile a tutti
            "DELETE_ITEM": True,  # Abilitato per tutti i piani
            "GET_RECENT_LIKES": True,
            "FOLLOW_USER": True,  # Follow/unfollow base per free
            "UNFOLLOW_USER": True,
            "GET_FOLLOW_USAGE": True,  # Usage stats per follow/unfollow
            # Follow Automation features
            "START_FOLLOW_AUTOMATION": True,  # Automazione follow per free
            "STOP_FOLLOW_AUTOMATION": True,
            "GET_FOLLOW_AUTOMATION_STATUS": True,
            "GET_FOLLOW_ACTIVITY_LOGS": True,
            # Auto Messages features
            "GET_AUTO_MESSAGE_SETTINGS": True,  # Auto messages per free
            "TOGGLE_AUTO_MESSAGES": True,
            "CREATE_AUTO_MESSAGE": True,
            "UPDATE_AUTO_MESSAGE": True,
            "DELETE_AUTO_MESSAGE": True,
            "SEND_MANUAL_MESSAGE": True,
            "GET_AUTO_MESSAGE_USAGE": True,
        },
        "limits": {
            # Business: free plan strict limits
            # Example constraints provided: follow up to 500/day, repost only 1/day
            "max_REPOST_ITEM_per_day": 1,
            "max_FOLLOW_USER_per_day": 500,
            "max_UNFOLLOW_USER_per_day": 100,  # Unfollow limitato per free
            # Follow Automation limits
            "max_FOLLOW_AUTOMATION_actions_per_day": 50,  # Automazione limitata per free
            "follow_automation_interval_minutes": 5,  # Intervallo minimo tra azioni
            # Auto Messages limits
            "max_CREATE_AUTO_MESSAGE_per_day": 5,  # Limite creazione messaggi per free
            "max_SEND_MANUAL_MESSAGE_per_day": 20,  # Limite invio manuale per free
        },
    },
    "prod_SeiG8zaFjvgLKx": {
        "name": "Essentials",
        "keyword": "essentials",
        "features": {
            "GET_USER_PROFILE": True,
            "GET_PLAN_AND_USAGE": True,
            "GET_USER_PRODUCTS": True,
            "CHECK_VINTED_LOGIN_STATUS": True,
            "GET_VINTED_STATS": True,
            "GET_VINTED_OFFERS": True,
            "GET_VINTED_ORDERS": True,  # Abilitato per piano Essentials
            "GET_UNREAD_MESSAGES": True,  # Abilitato per piano Essentials
            "ADVANCED_ANALYTICS": True,
            "ACCEPT_VINTED_OFFER": True,
            "DECLINE_VINTED_OFFER": True,
            "COUNTER_VINTED_OFFER": True,
            "GET_FOLLOWER_STATS": True,
            "REPOST_ITEM": True,
            "DELETE_ITEM": True,  # Abilitato per il piano Essentials
            "FOLLOW_USER": True,  # Follow/unfollow per Essentials
            "UNFOLLOW_USER": True,
            "GET_FOLLOW_USAGE": True,  # Usage stats per follow/unfollow
            # Follow Automation features
            "START_FOLLOW_AUTOMATION": True,  # Automazione follow per Essentials
            "STOP_FOLLOW_AUTOMATION": True,
            "GET_FOLLOW_AUTOMATION_STATUS": True,
            "GET_FOLLOW_ACTIVITY_LOGS": True,
            # Auto Messages features
            "GET_AUTO_MESSAGE_SETTINGS": True,  # Auto messages per Essentials
            "TOGGLE_AUTO_MESSAGES": True,
            "CREATE_AUTO_MESSAGE": True,
            "UPDATE_AUTO_MESSAGE": True,
            "DELETE_AUTO_MESSAGE": True,
            "SEND_MANUAL_MESSAGE": True,
            "GET_AUTO_MESSAGE_USAGE": True,
        },
        "limits": {
            # Ensure paid > free
            "max_REPOST_ITEM_per_day": 10,
            "max_FOLLOW_USER_per_day": 1000,
            "max_UNFOLLOW_USER_per_day": 50,  # Unfollow per Essentials
            # Follow Automation limits
            "max_FOLLOW_AUTOMATION_actions_per_day": 150,  # Automazione più generosa per Essentials
            "follow_automation_interval_minutes": 3,  # Intervallo più rapido per Essentials
            # Auto Messages limits
            "max_CREATE_AUTO_MESSAGE_per_day": 15,  # Limite creazione messaggi per Essentials
            "max_SEND_MANUAL_MESSAGE_per_day": 100,  # Limite invio manuale per Essentials
        },
    },
    "prod_SeiHMcb1pE8Tpi": {
        "name": "Essentials Plus",
        "keyword": "essentials_plus",
        "features": {
            "GET_USER_PROFILE": True,
            "GET_PLAN_AND_USAGE": True,
            "GET_USER_PRODUCTS": True,
            "CHECK_VINTED_LOGIN_STATUS": True,
            "GET_VINTED_STATS": True,
            "GET_VINTED_OFFERS": True,
            "GET_VINTED_ORDERS": True,  # Abilitato per piano Essentials Plus
            "GET_UNREAD_MESSAGES": True,  # Abilitato per piano Essentials Plus
            "ADVANCED_ANALYTICS": True,
            "ACCEPT_VINTED_OFFER": True,
            "DECLINE_VINTED_OFFER": True,
            "COUNTER_VINTED_OFFER": True,
            "GET_FOLLOWER_STATS": True,
            "REPOST_ITEM": True,
            "DELETE_ITEM": True,  # Abilitato per il piano Essentials Plus
            "FOLLOW_USER": True,  # Follow/unfollow per Essentials Plus
            "UNFOLLOW_USER": True,
            "GET_FOLLOW_USAGE": True,  # Usage stats per follow/unfollow
            # Follow Automation features
            "START_FOLLOW_AUTOMATION": True,  # Automazione follow per Essentials Plus
            "STOP_FOLLOW_AUTOMATION": True,
            "GET_FOLLOW_AUTOMATION_STATUS": True,
            "GET_FOLLOW_ACTIVITY_LOGS": True,
            # Auto Messages features
            "GET_AUTO_MESSAGE_SETTINGS": True,  # Auto messages per Essentials Plus
            "TOGGLE_AUTO_MESSAGES": True,
            "CREATE_AUTO_MESSAGE": True,
            "UPDATE_AUTO_MESSAGE": True,
            "DELETE_AUTO_MESSAGE": True,
            "SEND_MANUAL_MESSAGE": True,
            "GET_AUTO_MESSAGE_USAGE": True,
        },
        "limits": {
            "max_REPOST_ITEM_per_day": 50,  # Pro tier generous
            "max_FOLLOW_USER_per_day": -1,  # Unlimited follows for Plus
            "max_UNFOLLOW_USER_per_day": 150,  # Unfollow molto generoso per Plus
            # Follow Automation limits
            "max_FOLLOW_AUTOMATION_actions_per_day": -1,  # Illimitato per Plus
            "follow_automation_interval_minutes": 1,  # Intervallo molto rapido per Plus
            # Auto Messages limits
            "max_CREATE_AUTO_MESSAGE_per_day": -1,  # Illimitato per Plus
            "max_SEND_MANUAL_MESSAGE_per_day": -1,  # Illimitato per Plus
        },
    },
}


PLAN_KEYWORD_MAP = {details["keyword"]: details for _, details in PLANS.items() if "keyword" in details}


async def get_user_permissions(user_id: str) -> dict:
    try:
        try:
            user_profile_response = (
                supabase.table("profiles")
                .select("plan_id, subscription_status")
                .eq("id", user_id)
                .execute()
            )

            # Handle case where user doesn't exist in profiles table
            if not user_profile_response or not getattr(user_profile_response, "data", None) or len(user_profile_response.data) == 0:
                logger.info(f"User {user_id} not found in profiles, using default 'free' plan")
                plan_id, status = "free", "active"
            else:
                user_profile = user_profile_response.data[0]  # Take first result
                plan_id = user_profile.get("plan_id", "free")
                status = user_profile.get(
                    "subscription_status", "active" if plan_id == "free" else "inactive"
                )
        except Exception as db_err:
            # Defensive fallback: if the profiles table is missing billing columns or the DB responds with an error
            logger.error(f"Errore permessi per {user_id}: {db_err}")
            logger.error("It looks like the profiles table may be missing billing columns (plan_id/subscription_status/stripe_customer_id).\n"
                         "Apply the migration file 'add_billing_columns_to_profiles.sql' to add them.")
            plan_id, status = "free", "active"

        plan_rules = PLANS.get(plan_id, PLANS["free"])
        is_plan_active = plan_id == "free" or status in ["active", "trialing"]

        permissions = {
            "plan_id": plan_id,
            "plan_name": plan_rules["name"],
            "is_plan_active": is_plan_active,
            "features": plan_rules["features"],
            "limits": plan_rules["limits"],
        }
        
        # Log per debug - mostra i permessi dell'utente
        logger.info(f"🔐 User {user_id} permissions - Plan: {plan_id}, Active: {is_plan_active}")
        logger.info(f"📋 GET_VINTED_OFFERS enabled: {plan_rules['features'].get('GET_VINTED_OFFERS', False)}")
        
        return permissions
    except Exception as e:
        logger.error(f"Errore permessi per {user_id}: {e}")
        # Fallback to free plan on error
        free_plan = PLANS["free"]
        return {
            "plan_id": "free",
            "plan_name": free_plan["name"],
            "is_plan_active": True,
            "features": free_plan["features"],
            "limits": free_plan["limits"],
        }


async def get_daily_usage(user_id: str, action_type: str) -> int:
    """Get current daily usage count for a specific action type"""
    try:
        today = date.today()
        
        # Check different tables based on action type
        if action_type in ["FOLLOW_USER", "UNFOLLOW_USER"]:
            result = supabase.table("follow_usage_stats").select("*").eq(
                "user_id", user_id
            ).eq("date", today).execute()
            
            if result.data:
                stats = result.data[0]
                if action_type == "FOLLOW_USER":
                    return stats.get("follow_count", 0)
                elif action_type == "UNFOLLOW_USER":
                    return stats.get("unfollow_count", 0)
            return 0
            
        elif action_type == "REPOST_ITEM":
            # Count from activity logs or specific table if exists
            from datetime import timedelta
            today_start = datetime.combine(today, datetime.min.time()).replace(tzinfo=timezone.utc)
            today_end = today_start + timedelta(days=1)
            
            # This would need to be implemented based on your repost tracking
            # For now, return 0 as placeholder
            return 0
            
        else:
            # Generic activity log counting
            from datetime import timedelta
            today_start = datetime.combine(today, datetime.min.time()).replace(tzinfo=timezone.utc)
            today_end = today_start + timedelta(days=1)
            
            result = supabase.table("activity_logs").select("*").eq(
                "user_id", user_id
            ).eq("action_type", action_type).gte(
                "created_at", today_start.isoformat()
            ).lt("created_at", today_end.isoformat()).execute()
            
            return len(result.data) if result.data else 0
            
    except Exception as e:
        logger.error(f"Error getting daily usage for {user_id}, {action_type}: {e}")
        return 0


async def check_daily_limits(user_id: str, action_type: str) -> None:
    """Check if user has exceeded daily limits for an action type"""
    try:
        permissions = await get_user_permissions(user_id)
        
        # Check if user has permission for this action
        if not permissions["features"].get(action_type, False):
            raise PermissionDeniedError(f"Your plan doesn't support {action_type}")
        
        # Get daily limit for this action
        limit_key = f"max_{action_type}_per_day"
        daily_limit = permissions["limits"].get(limit_key, -1)
        
        # If limit is -1, it means unlimited
        if daily_limit == -1:
            return
        
        # Get current usage
        current_usage = await get_daily_usage(user_id, action_type)
        
        # Check if limit exceeded
        if current_usage >= daily_limit:
            plan_name = permissions["plan_name"]
            raise PermissionDeniedError(
                f"Daily limit exceeded for {action_type}. "
                f"Used {current_usage}/{daily_limit} for {plan_name} plan. "
                f"Upgrade your plan for higher limits."
            )
            
    except PermissionDeniedError:
        raise
    except Exception as e:
        logger.error(f"Error checking daily limits for {user_id}, {action_type}: {e}")
        # On error, allow the action to proceed to avoid blocking users
        pass


async def log_action_usage(user_id: str, action_type: str, success: bool = True, metadata: Dict[str, Any] = None):
    """Log usage of an action for tracking and analytics"""
    try:
        # Log to appropriate table based on action type
        if action_type in ["FOLLOW_USER", "UNFOLLOW_USER"]:
            # This is handled by the follow endpoint's log_follow_activity function
            pass
        else:
            # Generic activity logging
            supabase.table("activity_logs").insert({
                "user_id": user_id,
                "action_type": action_type,
                "success": success,
                "metadata": metadata or {},
                "created_at": datetime.now(timezone.utc).isoformat()
            }).execute()
            
    except Exception as e:
        logger.error(f"Error logging action usage for {user_id}, {action_type}: {e}")
        # Don't raise exception for logging failures
