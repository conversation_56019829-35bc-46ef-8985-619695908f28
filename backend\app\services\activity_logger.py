# app/services/activity_logger.py
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from app.db.supabase_client import supabase

logger = logging.getLogger(__name__)


class ActivityLogger:
    """
    Gestisce i log delle attività che vengono mostrati nell'interfaccia utente.
    Salva i log nel database per persistenza tra sessioni.
    """
    
    @staticmethod
    async def log_activity(
        user_id: str,
        log_type: str,
        message: str,
        message_type: str = "info",
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Salva un log di attività nel database.
        
        Args:
            user_id: ID dell'utente
            log_type: Tipo di log (follow, unfollow, auto_message, etc.)
            message: Messaggio leggibile
            message_type: Tipo di messaggio (info, success, warning, error)
            metadata: Dati aggiuntivi opzionali
        
        Returns:
            True se salvato con successo, False altrimenti
        """
        try:
            log_entry = {
                "user_id": user_id,
                "log_type": log_type,
                "message": message,
                "message_type": message_type,
                "metadata": metadata or {},
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            supabase.table("activity_logs").insert(log_entry).execute()
            
            logger.debug(f"📝 Activity log saved for user {user_id}: {message}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error saving activity log for user {user_id}: {e}")
            return False
    
    @staticmethod
    async def get_user_logs(
        user_id: str,
        log_type: str,
        limit: int = 50
    ) -> list:
        """
        Recupera i log di attività per un utente e tipo specifico.
        
        Args:
            user_id: ID dell'utente
            log_type: Tipo di log da recuperare
            limit: Numero massimo di log da recuperare
        
        Returns:
            Lista dei log ordinati per data (più recenti primi)
        """
        try:
            response = (
                supabase.table("activity_logs")
                .select("*")
                .eq("user_id", user_id)
                .eq("log_type", log_type)
                .order("created_at", desc=True)
                .limit(limit)
                .execute()
            )
            
            return response.data if response.data else []
            
        except Exception as e:
            logger.error(f"❌ Error fetching activity logs for user {user_id}: {e}")
            return []
    
    @staticmethod
    async def clear_user_logs(user_id: str, log_type: str) -> bool:
        """
        Elimina tutti i log di un tipo per un utente.
        
        Args:
            user_id: ID dell'utente
            log_type: Tipo di log da eliminare
        
        Returns:
            True se eliminati con successo, False altrimenti
        """
        try:
            supabase.table("activity_logs").delete().eq("user_id", user_id).eq("log_type", log_type).execute()
            logger.info(f"🗑️ Cleared {log_type} logs for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error clearing logs for user {user_id}: {e}")
            return False



