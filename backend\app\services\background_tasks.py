# app/services/background_tasks.py
import asyncio
import logging
import json
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from app.services.connection_manager import manager

logger = logging.getLogger(__name__)


class BackgroundTaskManager:
    def __init__(self):
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_states: Dict[str, Dict[str, Any]] = {}

    async def start_background_repost(self, user_id: str, item_id: str, params: dict) -> str:
        """
        Starts a background repost task that persists even if user disconnects.
        Returns a task_id that can be used to track progress.
        """
        task_id = f"repost_{user_id}_{item_id}_{int(datetime.now().timestamp())}"
        
        # Save initial state in memory
        initial_state = {
            "task_id": task_id,
            "user_id": user_id,
            "workflow_type": "REPOST_ITEM",
            "item_id": item_id,
            "params": params,
            "status": "started",
            "current_phase": 1,
            "total_phases": 7,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "error_message": None,
            "completed": False
        }
        
        # Store in memory
        self.task_states[task_id] = initial_state
        logger.info(f"✅ Created background task {task_id} for user {user_id}")

        # Start the actual background task
        task = asyncio.create_task(self._run_background_repost(task_id, user_id, item_id, params))
        self.running_tasks[task_id] = task
        
        return task_id

    async def _run_background_repost(self, task_id: str, user_id: str, item_id: str, params: dict):
        """
        Runs the actual repost workflow in the background with state persistence.
        """
        try:
            # Import here to avoid circular imports
            from app.services.vinted_workflows import workflow_repost_item
            from app.services.vinted_fetcher import get_active_domain
            
            # Update state: started
            await self._update_task_state(task_id, {
                "status": "running",
                "current_phase": 1,
                "phase_description": "Ottenimento token di sicurezza..."
            })

            # Execute the repost workflow with state updates
            result = await self._execute_repost_with_tracking(task_id, user_id, item_id, params)
            
            # Update final state: completed
            await self._update_task_state(task_id, {
                "status": "completed",
                "completed": True,
                "current_phase": 7,
                "phase_description": "Repost completato con successo!",
                "result": result
            })
            
            # Notify user if they're connected
            await self._notify_user_if_connected(user_id, {
                "type": "BACKGROUND_TASK_COMPLETED",
                "task_id": task_id,
                "workflow_type": "REPOST_ITEM",
                "result": result
            })
            
        except Exception as e:
            logger.error(f"❌ Background repost task {task_id} failed: {e}")
            await self._update_task_state(task_id, {
                "status": "failed",
                "error_message": str(e),
                "completed": True
            })
            
            # Notify user of failure if they're connected
            await self._notify_user_if_connected(user_id, {
                "type": "BACKGROUND_TASK_FAILED",
                "task_id": task_id,
                "workflow_type": "REPOST_ITEM",
                "error": str(e)
            })
        finally:
            # Cleanup
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            if task_id in self.task_states:
                del self.task_states[task_id]

    async def _execute_repost_with_tracking(self, task_id: str, user_id: str, item_id: str, params: dict) -> dict:
        """
        Executes the repost workflow with state tracking at each phase.
        """
        from app.services.vinted_workflows import perform_fetch_via_extension
        from app.services.vinted_fetcher import get_active_domain
        from app.utils import image_utils
        import re
        import aiohttp
        import base64

        domain = get_active_domain(user_id, params)
        
        # Phase 1: Get CSRF token
        await self._update_task_state(task_id, {
            "current_phase": 1,
            "phase_description": "Ottenimento token di sicurezza..."
        })
        
        csrf_resp = await perform_fetch_via_extension(user_id, {
            "url": f"https://{domain}/items/new",
            "method": "GET"
        })
        csrf_token_match = re.search(
            r'CSRF_TOKEN[^0-9A-Za-z]*([0-9a-fA-F]{8}(?:-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12})',
            csrf_resp.get("raw_response_body", "")
        )
        if not csrf_token_match:
            raise RuntimeError("Impossibile ottenere il token CSRF.")
        csrf_token = csrf_token_match.group(1)

        # Phase 2: Get item details
        await self._update_task_state(task_id, {
            "current_phase": 2,
            "phase_description": "Recupero dettagli del prodotto..."
        })
        
        item_details_resp = await perform_fetch_via_extension(user_id, {
            "url": f"https://{domain}/api/v2/item_upload/items/{item_id}",
            "method": "GET",
            "headers": {"x-csrf-token": csrf_token}
        })
        item_data = json.loads(item_details_resp.get("raw_response_body", "{}")).get("item", {})
        if not item_data:
            raise RuntimeError("Impossibile recuperare i dettagli dell'articolo.")

        # Phase 3: Download images
        photo_urls = [p.get("full_size_url") for p in item_data.get("photos", []) if p.get("full_size_url")]
        await self._update_task_state(task_id, {
            "current_phase": 3,
            "phase_description": f"Download di {len(photo_urls)} immagini..."
        })
        
        async with aiohttp.ClientSession() as session:
            image_tasks = [session.get(url) for url in photo_urls]
            image_responses = await asyncio.gather(*image_tasks)
            image_bytes_list = [await resp.read() for resp in image_responses]

        # Phase 4: Process images
        await self._update_task_state(task_id, {
            "current_phase": 4,
            "phase_description": "Modifica delle immagini..."
        })
        
        loop = asyncio.get_running_loop()
        processing_tasks = [
            loop.run_in_executor(None, image_utils.alter_photo, img_bytes)
            for img_bytes in image_bytes_list
        ]
        altered_image_bytes_list = await asyncio.gather(*processing_tasks)

        # Phase 5: Upload processed images
        await self._update_task_state(task_id, {
            "current_phase": 5,
            "phase_description": f"Caricamento di {len(altered_image_bytes_list)} nuove immagini..."
        })
        
        upload_tasks = []
        for img_bytes in altered_image_bytes_list:
            fetch_details_for_upload = {
                "job_type": "UPLOAD_PROCESSED_IMAGE",
                "image_base64": base64.b64encode(img_bytes).decode('utf-8'),
                "domain": domain,
                "csrf_token": csrf_token
            }
            upload_tasks.append(perform_fetch_via_extension(user_id, fetch_details_for_upload))
        
        upload_results = await asyncio.gather(*upload_tasks)
        new_photos_data = [res.get("data") for res in upload_results if res.get("status") == "success"]
        if len(new_photos_data) != len(photo_urls):
            raise RuntimeError("Caricamento fallito per una o più immagini.")

        # Phase 6: Create new listing
        await self._update_task_state(task_id, {
            "current_phase": 6,
            "phase_description": "Creazione della nuova inserzione..."
        })
        
        draft_payload = {"item": {k: v for k, v in {
            "title": item_data.get("title"),
            "description": item_data.get("description"),
            "brand_id": item_data.get("brand_dto", {}).get("id"),
            "size_id": item_data.get("size_id"),
            "catalog_id": item_data.get("catalog_id"),
            "status_id": item_data.get("status_id"),
            "price": item_data.get("price", {}).get("amount"),
            "currency": item_data.get("currency"),
            "package_size_id": item_data.get("package_size_id"),
            "color_ids": [item_data.get("color1_id"), item_data.get("color2_id")],
            "assigned_photos": [{"id": p.get("id"), "orientation": p.get("orientation", 0)} for p in new_photos_data]
        }.items() if v is not None}}
        
        draft_resp = await perform_fetch_via_extension(user_id, {
            "url": f"https://{domain}/api/v2/item_upload/items",
            "method": "POST",
            "headers": {"content-type": "application/json", "x-csrf-token": csrf_token},
            "body": json.dumps(draft_payload)
        })
        if draft_resp.get("http_status_code", 500) >= 400:
            raise RuntimeError(f"Errore creazione bozza: {draft_resp.get('raw_response_body')}")

        # Phase 7: Archive old listing
        await self._update_task_state(task_id, {
            "current_phase": 7,
            "phase_description": "Archiviazione della vecchia inserzione..."
        })
        
        await perform_fetch_via_extension(user_id, {
            "url": f"https://{domain}/api/v2/items/{item_id}/is_hidden",
            "method": "PUT",
            "headers": {"content-type": "application/json", "x-csrf-token": csrf_token},
            "body": json.dumps({"is_hidden": True})
        })
        
        return {"status": "success", "message": f"Articolo {item_id} ripubblicato con successo!"}

    async def _update_task_state(self, task_id: str, updates: dict):
        """
        Updates task state in memory only.
        """
        updates["updated_at"] = datetime.now(timezone.utc).isoformat()
        
        # Update memory state
        if task_id in self.task_states:
            self.task_states[task_id].update(updates)

        # Notify user if connected (non-blocking)
        try:
            user_id = self.task_states.get(task_id, {}).get("user_id")
            task_state = self.task_states.get(task_id, {})
            workflow_type = task_state.get("workflow_type", "UNKNOWN")
            
            if user_id:
                # Prepare notification based on workflow type
                notification = {
                    "type": "BACKGROUND_TASK_PROGRESS",
                    "task_id": task_id,
                    "workflow_type": workflow_type,
                    "status": updates.get("status", task_state.get("status"))
                }
                
                # Add specific fields based on workflow type
                if workflow_type == "REPOST_ITEM":
                    notification.update({
                        "current_phase": updates.get("current_phase"),
                        "total_phases": 7,
                        "phase_description": updates.get("phase_description")
                    })
                elif workflow_type == "FOLLOW_AUTOMATION":
                    notification.update({
                        "stats": task_state.get("stats", {}),
                        "error_message": updates.get("error_message")
                    })
                
                await self._notify_user_if_connected(user_id, notification)
        except Exception as e:
            logger.warning(f"⚠️ Failed to notify user of progress: {e}")

    async def _notify_user_if_connected(self, user_id: str, message: dict):
        """
        Sends notification to user only if they're connected.
        """
        try:
            if user_id in manager.react_connections:
                await manager.send_to_user_react(user_id, message)
        except Exception as e:
            logger.warning(f"⚠️ Could not notify user {user_id}: {e}")

    async def get_task_status(self, task_id: str) -> Optional[dict]:
        """
        Gets current status of a background task from memory.
        """
        return self.task_states.get(task_id)

    async def get_user_tasks(self, user_id: str) -> list:
        """
        Gets all background tasks for a user from memory.
        """
        user_tasks = []
        for task_id, task_state in self.task_states.items():
            if task_state.get("user_id") == user_id:
                user_tasks.append(task_state)
        
        # Sort by created_at descending
        user_tasks.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        return user_tasks

    async def cancel_task(self, task_id: str) -> bool:
        """
        Cancels a running background task.
        """
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            if not task.done():
                task.cancel()
                await self._update_task_state(task_id, {
                    "status": "cancelled",
                    "completed": True
                })
                logger.info(f"❌ Cancelled background task {task_id}")
                return True
        return False

    def cleanup_completed_tasks(self):
        """
        Removes completed tasks from memory to prevent memory leaks.
        Call this periodically to clean up old tasks.
        """
        to_remove = []
        for task_id, task_state in self.task_states.items():
            if task_state.get("completed", False):
                # Keep completed tasks for 1 hour for user to see results
                created_at = datetime.fromisoformat(task_state.get("created_at", ""))
                if (datetime.now(timezone.utc) - created_at).total_seconds() > 3600:  # 1 hour
                    to_remove.append(task_id)
        
        for task_id in to_remove:
            if task_id in self.task_states:
                del self.task_states[task_id]
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            logger.info(f"🗑️ Cleaned up old completed task {task_id}")

    def get_stats(self) -> dict:
        """
        Returns statistics about background tasks.
        """
        total_tasks = len(self.task_states)
        running_tasks = len([t for t in self.task_states.values() if not t.get("completed", False)])
        completed_tasks = len([t for t in self.task_states.values() if t.get("completed", False)])
        
        return {
            "total_tasks": total_tasks,
            "running_tasks": running_tasks,
            "completed_tasks": completed_tasks,
            "task_memory_usage": f"{len(self.task_states)} states, {len(self.running_tasks)} active tasks"
        }




# Global instance
background_task_manager = BackgroundTaskManager()
