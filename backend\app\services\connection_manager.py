# app/services/connection_manager.py
import asyncio
import logging
import time
from fastapi import WebSocket, status
from typing import Dict, List, Any, Optional
from app.models.plans import get_user_permissions

logger = logging.getLogger(__name__)


class ConnectionManager:
    def __init__(self):
        self.react_connections: Dict[str, List[WebSocket]] = {}
        self.extension_connections: Dict[str, Dict[str, Any]] = {}
        # Track active workflows per user
        self.active_workflows: Dict[str, Dict[str, asyncio.Task]] = {}
        # Track workflow queues per user and workflow type
        self.workflow_queues: Dict[str, Dict[str, List[Dict[str, Any]]]] = {}
        # Track pending extension responses
        self.pending_extension_responses: Dict[str, Dict[str, asyncio.Future]] = {}

    async def connect(self, websocket: WebSocket, user_id: str, client_type: str):
        await websocket.accept()
        if client_type == "react":
            if user_id not in self.react_connections:
                self.react_connections[user_id] = []
            self.react_connections[user_id].append(websocket)
            logger.info(f"✅ React client (User: {user_id}) CONNESSO.")
            await self.send_to_user_react(
                user_id,
                {
                    "type": "INITIAL_STATE",
                    "is_extension_online": self.is_extension_online(user_id),
                    "permissions": await get_user_permissions(user_id),
                },
            )
            # Notify about any active background tasks
            await self._notify_background_tasks(user_id)
        elif client_type == "extension":
            if user_id in self.extension_connections and self.get_extension_websocket(
                user_id
            ):
                logger.warning(f"Estensione già connessa per {user_id}. Sostituisco.")
                try:
                    await self.get_extension_websocket(user_id).close(
                        code=status.WS_1012_SERVICE_RESTART
                    )
                except RuntimeError:
                    pass
            self.extension_connections[user_id] = {"websocket": websocket}
            logger.info(f"🔌 Extension client (User: {user_id}) CONNESSO.")
            
            # Notify all React clients immediately
            await self.send_to_user_react(
                user_id, {"type": "EXTENSION_STATUS_UPDATE", "is_online": True}
            )
            logger.info(f"📢 Notified React clients that extension for {user_id} is online")
            
            # Send an explicit ACK to the extension so its UI can reflect WS=connected
            try:
                await self.send_to_extension(user_id, {
                    "type": "EXTENSION_CONNECTED_ACK",
                    "server_time": time.time(),
                })
                logger.info(f"✅ Sent EXTENSION_CONNECTED_ACK to {user_id}")
            except Exception as e:
                logger.warning(f"Could not send EXTENSION_CONNECTED_ACK to {user_id}: {e}")
            
            # Trigger Vinted login check when extension connects
            asyncio.create_task(self._check_vinted_login_on_connect(user_id))

    def disconnect(self, websocket: WebSocket, user_id: str, client_type: str):
        if client_type == "react":
            if user_id in self.react_connections:
                try:
                    self.react_connections[user_id].remove(websocket)
                except ValueError:
                    pass
                if not self.react_connections[user_id]:
                    del self.react_connections[user_id]
                    # Cleanup workflows when last React client disconnects
                    self.cleanup_user_workflows(user_id)
            logger.info(f"🔌 React client (User: {user_id}) DISCONNESSO.")
        elif (
            client_type == "extension"
            and self.get_extension_websocket(user_id) == websocket
        ):
            if user_id in self.extension_connections:
                del self.extension_connections[user_id]
            logger.info(f"🔌 Extension client (User: {user_id}) DISCONNESSO.")
            
            # Notify React clients that extension went offline
            asyncio.create_task(
                self.send_to_user_react(
                    user_id, {"type": "EXTENSION_STATUS_UPDATE", "is_online": False}
                )
            )
            logger.info(f"📢 Notified React clients that extension for {user_id} is offline")

    def is_extension_online(self, user_id: str) -> bool:
        return user_id in self.extension_connections

    def get_extension_websocket(self, user_id: str) -> Optional[WebSocket]:
        return self.extension_connections.get(user_id, {}).get("websocket")

    def update_extension_data(self, user_id: str, data_to_update: Dict[str, Any]):
        if user_id in self.extension_connections:
            self.extension_connections[user_id].update(data_to_update)
            
            # Add timestamp when saving vinted_user_info
            if "vinted_user_info" in data_to_update:
                self.extension_connections[user_id]["last_login_check"] = time.time()

    def get_extension_data(
        self, user_id: str, key: str, default: Any = None
    ) -> Optional[Any]:
        return self.extension_connections.get(user_id, {}).get(key, default)

    def clear_extension_data(self, user_id: str, key: str):
        """
        Rimuove un dato specifico dall'estensione per un utente.
        """
        if user_id in self.extension_connections and key in self.extension_connections[user_id]:
            del self.extension_connections[user_id][key]
            logger.debug(f"Cleared extension data '{key}' for user {user_id}")

    async def send_to_user_react(self, user_id: str, message: dict):
        if user_id in self.react_connections:
            for connection in self.react_connections[user_id][:]:
                try:
                    await connection.send_json(message)
                except Exception:
                    self.disconnect(connection, user_id, "react")

    async def send_to_extension(self, user_id: str, message: dict):
        websocket = self.get_extension_websocket(user_id)
        if websocket:
            try:
                await websocket.send_json(message)
            except Exception:
                self.disconnect(websocket, user_id, "extension")

    async def send_to_user_extension(self, user_id: str, message: dict):
        """Alias for send_to_extension for consistency"""
        await self.send_to_extension(user_id, message)

    async def wait_for_extension_response(self, user_id: str, request_id: str) -> Dict[str, Any]:
        """Wait for a response from the extension for a specific request_id"""
        if user_id not in self.pending_extension_responses:
            self.pending_extension_responses[user_id] = {}
        
        # Create a future to wait for the response
        future = asyncio.Future()
        self.pending_extension_responses[user_id][request_id] = future
        
        try:
            # Wait for the response
            response = await future
            return response
        finally:
            # Clean up the future
            if (user_id in self.pending_extension_responses and 
                request_id in self.pending_extension_responses[user_id]):
                del self.pending_extension_responses[user_id][request_id]
                if not self.pending_extension_responses[user_id]:
                    del self.pending_extension_responses[user_id]

    def resolve_extension_response(self, user_id: str, request_id: str, response: Dict[str, Any]):
        """Resolve a pending extension response"""
        if (user_id in self.pending_extension_responses and 
            request_id in self.pending_extension_responses[user_id]):
            future = self.pending_extension_responses[user_id][request_id]
            if not future.done():
                future.set_result(response)

    async def notify_vinted_login_update(
        self,
        user_id: str,
        is_logged_in: bool,
        vinted_user_id: Optional[int],
        vinted_username: Optional[str],
    ):
        await self.send_to_user_react(
            user_id,
            {
                "type": "VINTED_LOGIN_INFO_UPDATE",
                "is_logged_in": is_logged_in,
                "vinted_user_id": vinted_user_id,
                "vinted_username": vinted_username,
            },
        )

    def start_workflow(self, user_id: str, workflow_key: str, task: asyncio.Task):
        """
        Registers a new workflow task and cancels any existing workflow of the same type.
        """
        # Cancel existing workflow of the same type
        self.cancel_workflow(user_id, workflow_key)
        
        # Initialize user workflows if not exists
        if user_id not in self.active_workflows:
            self.active_workflows[user_id] = {}
        
        # Register the new workflow
        self.active_workflows[user_id][workflow_key] = task
        logger.info(f"🚀 Started workflow {workflow_key} for user {user_id}")

    async def queue_or_get_workflow_result(self, user_id: str, workflow_key: str, params: dict, request_id: str, workflow_func):
        """
        If a workflow of the same type is already running for this user, cancel it and start a new one.
        This ensures the user always gets the most up-to-date data.
        Returns a workflow ID that can be used to check status later.
        """
        # Check if there's already an active workflow of this type for this user
        if (user_id in self.active_workflows and 
            workflow_key in self.active_workflows[user_id] and 
            not self.active_workflows[user_id][workflow_key].done()):
            
            logger.info(f"🔄 Workflow {workflow_key} already running for user {user_id}, cancelling previous and starting new...")
            
            # Cancel the existing workflow
            existing_task = self.active_workflows[user_id][workflow_key]
            if not existing_task.done():
                existing_task.cancel()
                logger.info(f"❌ Cancelled previous workflow {workflow_key} for user {user_id}")
            
            # Remove the old task from tracking
            del self.active_workflows[user_id][workflow_key]
            
            # Notify user that previous workflow was cancelled
            try:
                await self.send_to_user_react(user_id, {
                    "type": "WORKFLOW_CANCELLED",
                    "workflow_key": workflow_key,
                    "message": f"Previous {workflow_key} workflow cancelled to start fresh",
                    "request_id": request_id
                })
            except Exception as e:
                logger.warning(f"⚠️ Could not notify user of workflow cancellation: {e}")
        
        # Start a new workflow ASYNCHRONOUSLY
        logger.info(f"🚀 Starting new workflow {workflow_key} for user {user_id}")
        
        # Create the workflow task with the passed parameters
        task = asyncio.create_task(workflow_func(user_id, params))
        
        # Register the task
        if user_id not in self.active_workflows:
            self.active_workflows[user_id] = {}
        self.active_workflows[user_id][workflow_key] = task
        
        # Add request_id to the task for tracking
        task.request_id = request_id
        
        # Start the task in the background and return immediately
        # The task will complete independently and notify the user when done
        asyncio.create_task(self._monitor_workflow_completion(user_id, workflow_key, task, request_id))
        
        # Return immediately with workflow info
        return {
            "workflow_id": f"{workflow_key}_{user_id}_{int(time.time())}",
            "status": "started",
            "message": f"Workflow {workflow_key} started (previous cancelled)"
        }

    def is_workflow_active(self, user_id: str, workflow_key: str) -> bool:
        """
        Check if a specific workflow is currently active for a user.
        """
        return (user_id in self.active_workflows and 
                workflow_key in self.active_workflows[user_id] and 
                not self.active_workflows[user_id][workflow_key].done())

    def cancel_workflow(self, user_id: str, workflow_key: str):
        """
        Cancels a specific workflow for a user if it exists and is still running.
        """
        if user_id in self.active_workflows and workflow_key in self.active_workflows[user_id]:
            task = self.active_workflows[user_id][workflow_key]
            if not task.done():
                task.cancel()
                logger.info(f"❌ Cancelled previous workflow {workflow_key} for user {user_id}")
            del self.active_workflows[user_id][workflow_key]
            
            # Clean up user entry if no workflows remain
            if not self.active_workflows[user_id]:
                del self.active_workflows[user_id]

    def cleanup_user_workflows(self, user_id: str):
        """
        Cancels all workflows for a user (useful when they disconnect).
        """
        if user_id in self.active_workflows:
            for workflow_key, task in self.active_workflows[user_id].items():
                if not task.done():
                    task.cancel()
                    logger.info(f"❌ Cancelled workflow {workflow_key} for disconnected user {user_id}")
            del self.active_workflows[user_id]
        
        # Clean up any queued workflows
        if user_id in self.workflow_queues:
            del self.workflow_queues[user_id]

    async def _notify_background_tasks(self, user_id: str):
        """
        Notifies the user about any active background tasks when they reconnect.
        """
        try:
            from app.services.background_tasks import background_task_manager
            tasks = await background_task_manager.get_user_tasks(user_id)
            
            # Filter for active tasks only
            active_tasks = [task for task in tasks if not task.get("completed", False)]
            
            if active_tasks:
                await self.send_to_user_react(user_id, {
                    "type": "BACKGROUND_TASKS_RESTORED",
                    "active_tasks": active_tasks
                })
                logger.info(f"🔄 Notified user {user_id} about {len(active_tasks)} active background tasks")
        except Exception as e:
            logger.warning(f"⚠️ Could not notify background tasks for user {user_id}: {e}")

    async def _monitor_workflow_completion(self, user_id: str, workflow_key: str, task: asyncio.Task, request_id: str):
        """
        Monitor a workflow task and notify the user when it completes.
        This runs in the background and doesn't block the main execution.
        """
        try:
            # Wait for the task to complete
            result = await task
            logger.info(f"✅ Workflow {workflow_key} completed for user {user_id}")
            
            # Notify the user of completion
            await self.send_to_user_react(user_id, {
                "type": "WORKFLOW_RESULT",
                "request_id": request_id,
                "workflow_key": workflow_key,
                "status": "success",
                "data": result
            })
            
        except asyncio.CancelledError:
            # Workflow was cancelled (probably by a new request)
            logger.info(f"🔄 Workflow {workflow_key} was cancelled for user {user_id}")
            # Don't send notification as the user already received WORKFLOW_CANCELLED
            
        except Exception as e:
            logger.error(f"❌ Workflow {workflow_key} failed for user {user_id}: {e}")
            
            # Notify the user of failure
            await self.send_to_user_react(user_id, {
                "type": "WORKFLOW_RESULT",
                "request_id": request_id,
                "workflow_key": workflow_key,
                "status": "failed",
                "error": str(e)
            })
            
        finally:
            # Clean up the workflow tracking
            if (user_id in self.active_workflows and 
                workflow_key in self.active_workflows[user_id]):
                del self.active_workflows[user_id][workflow_key]
                if not self.active_workflows[user_id]:
                    del self.active_workflows[user_id]

    async def _check_vinted_login_on_connect(self, user_id: str):
        """
        Check Vinted login status when extension connects
        """
        try:
            # Give extension a moment to initialize
            await asyncio.sleep(1.5)
            
            # Import here to avoid circular imports
            from app.services.workflow_manager import workflow_manager
            
            # Execute login check workflow
            await workflow_manager.execute_workflow(
                user_id=user_id,
                workflow_key="CHECK_VINTED_LOGIN_STATUS",
                params={}
            )
            
        except Exception as e:
            logger.error(f"Error checking login on extension connect for user {user_id}: {e}")


manager = ConnectionManager()
