# app/services/likes_workflows.py
import asyncio
import logging
import re
from typing import Dict, Any, List
from datetime import datetime, timezone, timedelta
from app.db.supabase_client import supabase
from app.services.connection_manager import manager
from app.utils.time import format_time_ago

logger = logging.getLogger(__name__)


def extract_like_data_from_notification(notification: dict, vinted_user_id: str, user_id: str, domain: str = "www.vinted.it") -> dict:
    """
    Extracts like data from a Vinted notification with the new structure.
    
    Expected notification structure:
    {
        "id": "8950e032f3d47843dc9c6f9c201a41a4",
        "entry_type": 20,
        "body": "rubiod ha aggiunto il tuo articolo Occhiali celesti ai suoi preferiti.",
        "subject_id": 5336765024,  # Item ID
        "user_id": 54657853,      # Item owner user ID (your ID)
        "link": "/items/5336765024/want_it/new?offering_id=32245041",  # offering_id is the liker
        "photo": {"url": "https://...", "thumbnails": [...]},  # Item photo in nested structure
        "small_photo_url": "https://...",  # Direct photo URL (might not be present)
        "updated_at": "2025-08-02T17:09:40+00:00"
    }
    """
    notification_id = str(notification.get("id", ""))
    
    # Extract item data
    item_id = str(notification.get("subject_id", "")) if notification.get("subject_id") else ""
    
    # Extract item photo URL - check multiple possible locations
    item_photo_url = ""
    if notification.get("small_photo_url"):
        item_photo_url = notification.get("small_photo_url")
    elif notification.get("photo") and notification["photo"].get("url"):
        item_photo_url = notification["photo"]["url"]
    elif notification.get("photo") and notification["photo"].get("thumbnails"):
        # Use the first thumbnail as fallback
        thumbnails = notification["photo"]["thumbnails"]
        if thumbnails and len(thumbnails) > 0:
            item_photo_url = thumbnails[0].get("url", "")
    
    # Extract liker user ID from the link field
    # The link format is: /items/5336765024/want_it/new?offering_id=32245041
    # The offering_id is the person who liked the item
    liker_user_id = ""
    link = notification.get("link", "")
    logger.info(f"🔍 DEBUG - Link field: {link}")
    
    if link and "offering_id=" in link:
        # Extract offering_id from link like "/items/5336765024/want_it/new?offering_id=32245041"
        # This offering_id is the person who liked the item
        offering_id_match = re.search(r"offering_id=(\d+)", link)
        if offering_id_match:
            liker_user_id = offering_id_match.group(1)
            logger.info(f"🔍 DEBUG - Extracted liker_user_id from offering_id: {liker_user_id}")
        else:
            logger.warning(f"🔍 DEBUG - Could not extract offering_id from link with regex")
    else:
        logger.warning(f"🔍 DEBUG - No link field or 'offering_id=' not found in link")
    
    # Extract timestamp - try updated_at first, then created_at_ts
    timestamp = notification.get("updated_at") or notification.get("created_at_ts")
    
    # Convert timestamp
    liked_at = None
    if timestamp:
        try:
            if isinstance(timestamp, str):
                # Handle ISO format like "2025-08-02T17:09:41+00:00"
                liked_at = datetime.fromisoformat(timestamp.replace('Z', '+00:00')).isoformat()
            elif isinstance(timestamp, (int, float)):
                liked_at = datetime.fromtimestamp(timestamp, tz=timezone.utc).isoformat()
        except (ValueError, TypeError) as e:
            logger.warning(f"Could not parse timestamp {timestamp}: {e}")
    
    if not liked_at:
        liked_at = datetime.now(timezone.utc).isoformat()
    
    # DEBUG: Log the final extracted data
    logger.info(f"🔍 DEBUG - Final extracted like data: liker_user_id='{liker_user_id}', item_id='{item_id}', notification_id='{notification_id}'")
    
    return {
        "vinted_user_id": vinted_user_id,
        "user_id": user_id,
        "notification_id": notification_id,
        "notification_type": "like",
        "liked_at": liked_at,
        "item_id": item_id,
        "item_title": "",  # Not available in new structure, will be empty
        "item_photo_url": item_photo_url,
        "liker_username": "",  # Avoiding extraction from body due to language differences
        "liker_user_id": liker_user_id,
        "notification_body": notification.get("body", ""),
        "link": link,  # Save the original link for redirect functionality
        "domain": domain
    }


async def workflow_sync_vinted_likes(user_id: str, params: dict):
    """
    Sync only LIKES from Vinted notifications.
    Uses a special tracking record in vinted_notifications table (notification_type = "last_processed") 
    to avoid re-processing the same notifications.
    
    This workflow is designed to be robust and continue working even if:
    - User disconnects from the webapp
    - User changes page or closes browser
    - WebSocket connection is lost
    
    The workflow will complete and save results regardless of user connectivity.
    """
    try:
        logger.info(f"Starting likes sync workflow for user {user_id}")
        
        # Get Vinted user info from extension
        vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
        if not (vinted_user_info and vinted_user_info.get("id")):
            raise Exception("Vinted user information not available. Please ensure extension is connected.")
        
        vinted_user_id = str(vinted_user_info["id"])
        logger.info(f"Processing likes for Vinted user {vinted_user_id}")
        
        # Get the last processed notification ID from the tracking record in vinted_notifications
        # We save a special "tracking" record with notification_type = "last_processed"
        last_notification_response = (
            supabase.table("vinted_notifications")
            .select("notification_id")
            .eq("vinted_user_id", vinted_user_id)
            .eq("user_id", user_id)
            .eq("notification_type", "last_processed")
            .execute()
        )
        
        last_notification_id = None
        if last_notification_response.data:
            last_notification_id = last_notification_response.data[0]["notification_id"]
            logger.info(f"Found last processed notification ID: {last_notification_id}")
        else:
            logger.info("No previous tracking record found, will sync all")
        
        # Request notifications from extension
        logger.info(f"Requesting notifications from extension (after {last_notification_id})")
        
        # Send request to extension
        await manager.send_to_extension(user_id, {
            "action": "GET_NOTIFICATIONS",
            "params": {
                "after_notification_id": last_notification_id
            }
        })
        
        # Wait for response from extension
        logger.info("Waiting for notifications data from extension...")
        
        # Wait up to 30 seconds for the extension response
        max_wait = 30
        wait_time = 0
        notifications_data = None
        
        while wait_time < max_wait:
            notifications_data = manager.get_extension_data(user_id, "vinted_notifications_response")
            if notifications_data:
                break
            await asyncio.sleep(1)
            wait_time += 1
        
        if not notifications_data:
            raise Exception("Timeout waiting for notifications from extension")
        
        # Clear the response data
        manager.clear_extension_data(user_id, "vinted_notifications_response")
        
        notifications = notifications_data.get("notifications", [])
        logger.info(f"Received {len(notifications)} notifications from extension")
        
        if not notifications:
            logger.info("No new notifications to process")
            return {
                "status": "success",
                "likes_saved": 0,
                "message": "No new notifications found"
            }
        
        # Process only LIKE notifications (entry_type = 20)
        likes_to_save = []
        likes_found = 0
        latest_notification_id = None
        
        for notification in notifications:
            notification_id = str(notification.get("id", ""))
            
            # Track the latest notification ID (first notification is the most recent)
            # Save only the FIRST notification ID we encounter
            if latest_notification_id is None:
                latest_notification_id = notification_id
                logger.info(f"🎯 Set latest_notification_id to first notification: {latest_notification_id}")
            
            entry_type = notification.get("entry_type")
            if entry_type == 20:  # Like notification
                likes_found += 1
                
                # DEBUG: Print the entire notification to understand the structure
                logger.info(f"🔍 DEBUG - Processing like notification: {notification}")
                
                # Extract like data using the new structure
                like_data = extract_like_data_from_notification(
                    notification, 
                    vinted_user_id, 
                    user_id,
                    notification.get("domain", "www.vinted.it")
                )
                
                likes_to_save.append(like_data)
        
        logger.info(f"Found {likes_found} likes out of {len(notifications)} notifications")
        
        if not likes_to_save:
            logger.info("No like notifications to save")
            return {
                "status": "success",
                "likes_saved": 0,
                "likes_found": likes_found,
                "message": "No like notifications found"
            }
        
        # Save likes to database
        logger.info(f"Saving {len(likes_to_save)} likes to database")
        
        # Use upsert to handle duplicates
        insert_response = (
            supabase.table("vinted_notifications")
            .upsert(likes_to_save, on_conflict="vinted_user_id,notification_id,notification_type")
            .execute()
        )
        
        likes_saved = len(insert_response.data) if insert_response.data else 0
        
        logger.info(f"Successfully saved {likes_saved} likes")
        
        # Update the tracking record with the latest notification ID
        # This ensures we don't re-process the same notifications next time
        if latest_notification_id:
            try:
                logger.info(f"🔍 Preparing to save tracking record with notification_id: {latest_notification_id}")
                
                # First, delete the old tracking record if it exists
                delete_result = supabase.table("vinted_notifications").delete().eq("vinted_user_id", vinted_user_id).eq("user_id", user_id).eq("notification_type", "last_processed").execute()
                logger.info(f"🗑️ Deleted old tracking records: {len(delete_result.data) if delete_result.data else 0}")
                
                # Insert new tracking record with the latest notification ID
                tracking_data = {
                    "vinted_user_id": vinted_user_id,
                    "user_id": user_id,
                    "notification_id": latest_notification_id,
                    "notification_type": "last_processed",  # Special type for tracking
                    "liked_at": datetime.now(timezone.utc).isoformat(),
                    "item_id": "0",  # Placeholder for tracking record (not a real item)
                    "item_title": "Tracking Record",  # Placeholder title
                    "item_photo_url": "",  # Empty photo URL for tracking
                    "liker_username": "system",
                    "liker_user_id": "0",  # Placeholder user ID
                    "notification_body": f"Last processed notification: {latest_notification_id}",
                    "domain": "www.vinted.it"
                }
                
                logger.info(f"🔍 Inserting tracking record: {tracking_data}")
                insert_result = supabase.table("vinted_notifications").insert(tracking_data).execute()
                logger.info(f"✅ Tracking record saved successfully: {insert_result.data}")
                logger.info(f"✅ Updated tracking record with latest notification ID: {latest_notification_id}")
                
            except Exception as e:
                logger.error(f"❌ Error updating tracking record: {e}")
                logger.error(f"❌ tracking_data was: {tracking_data if 'tracking_data' in locals() else 'Not defined'}")
        else:
            logger.warning("⚠️ No latest_notification_id found, tracking record not updated")
        
        # Send WebSocket notification to frontend (if user is still connected)
        try:
            await manager.send_to_user_react(user_id, {
                "type": "LIKES_SYNC_COMPLETE",
                "data": {
                    "likes_saved": likes_saved,
                    "likes_found": likes_found,
                    "total_notifications": len(notifications)
                }
            })
            logger.info(f"📡 Sent sync completion notification to user {user_id}")
        except Exception as ws_error:
            # User might have disconnected - this is normal, workflow should continue
            logger.info(f"📡 Could not send WebSocket notification (user might be disconnected): {ws_error}")
        
        return {
            "status": "success",
            "likes_saved": likes_saved,
            "likes_found": likes_found,
            "total_notifications": len(notifications),
            "message": f"Successfully synced {likes_saved} new likes"
        }
        
    except Exception as e:
        logger.error(f"Error in likes sync workflow: {e}")
        
        # Send error notification to frontend (if user is still connected)
        try:
            await manager.send_to_user_react(user_id, {
                "type": "LIKES_SYNC_ERROR",
                "data": {
                    "error": str(e)
                }
            })
            logger.info(f"📡 Sent error notification to user {user_id}")
        except Exception as ws_error:
            # User might have disconnected - log the original error anyway
            logger.info(f"📡 Could not send WebSocket error notification (user might be disconnected): {ws_error}")
            logger.error(f"❌ Original sync error: {e}")
        
        raise





async def workflow_get_recent_likes_with_sync(user_id: str, params: dict) -> dict:
    """
    Unified workflow that syncs likes from Vinted and returns recent likes.
    This ensures users always see the most up-to-date likes.
    
    This workflow is designed to be robust and continue working even if:
    - User disconnects from the webapp
    - User changes page or closes browser
    - WebSocket connection is lost
    
    The workflow will complete and save results regardless of user connectivity.
    """
    logger.info(f"💖 Getting recent likes with sync for user {user_id}")
    
    # Import connection manager to get extension data
    from app.services.connection_manager import manager
    
    # Get Vinted user info from extension
    vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
    if not (vinted_user_info and vinted_user_info.get("id")):
        return {"recent_likes": [], "total_count": 0, "error": "Vinted user information not available. Please ensure extension is connected."}
    
    vinted_user_id = str(vinted_user_info["id"])
    logger.info(f"Processing likes for Vinted user {vinted_user_id}")
    
    try:
        # STEP 1: Sync new likes from Vinted
        logger.info("🔄 Starting likes sync...")
        
        # Get the last processed notification ID from the tracking record in vinted_notifications
        # We save a special "tracking" record with notification_type = "last_processed" 
        last_notification_response = (
            supabase.table("vinted_notifications")
            .select("notification_id")
            .eq("vinted_user_id", vinted_user_id)
            .eq("user_id", user_id)
            .eq("notification_type", "last_processed")
            .execute()
        )
        
        last_notification_id = None
        if last_notification_response.data:
            last_notification_id = last_notification_response.data[0]["notification_id"]
            logger.info(f"Found last processed notification ID: {last_notification_id}")
        
        # Get active domain for API calls
        domain = manager.get_extension_data(user_id, "active_domain", "www.vinted.it")
        
        # Fetch notifications from Vinted API via extension
        all_likes = []
        page = 1
        latest_notification_id = None
        found_last_notification = False
        
        while not found_last_notification:  # Continue until no more notifications or we reach the last processed one
            try:
                url = f"https://{domain}/api/v2/notifications?page={page}&per_page=100"
                
                logger.info(f"📋 Fetching notifications page {page}")
                
                # Use the vinted_fetcher to get notifications via extension
                from app.services.vinted_fetcher import perform_fetch_via_extension
                
                response_data = await perform_fetch_via_extension(
                    user_id, {"url": url, "method": "GET", "job_type": "FETCH_RAW_URL"}
                )
                
                if response_data.get("status") != "success":
                    logger.warning(f"Failed to fetch notifications page {page}: {response_data.get('error')}")
                    break
                
                import json
                raw_response = response_data.get("raw_response_body", "{}")
                data = json.loads(raw_response) if isinstance(raw_response, str) else raw_response
                notifications = data.get("notifications", [])
                
                if not notifications:
                    logger.info(f"No more notifications found on page {page} - end of results")
                    break
                
                logger.info(f"📋 Processing {len(notifications)} notifications on page {page}")
                
                # Process all notifications to check for stop condition
                page_likes = []
                for i, notification in enumerate(notifications):
                    notification_id = notification.get("id")
                    entry_type = notification.get("entry_type")
                    
                    # Store the latest notification ID from the FIRST notification of the FIRST page
                    # This is the most recent notification available
                    if page == 1 and i == 0:
                        latest_notification_id = str(notification_id)
                    
                    # Check if we reached the last processed notification (any type)
                    # Convert both to strings for reliable comparison
                    if last_notification_id and str(notification_id) == str(last_notification_id):
                        logger.info(f"✅ Reached last processed notification: {notification_id}")
                        found_last_notification = True
                        break
                    
                    # Only save likes (entry_type = 20) but check ALL notifications for stop condition
                    if entry_type == 20:
                        # DEBUG: Print the entire notification to understand the structure
                        logger.info(f"🔍 DEBUG - Processing like notification: {notification}")
                        
                        like_data = extract_like_data_from_notification(
                            notification, 
                            vinted_user_id, 
                            user_id,
                            domain
                        )
                        
                        page_likes.append(like_data)
                
                all_likes.extend(page_likes)
                logger.info(f"💖 Found {len(page_likes)} likes on page {page}")
                
                # If we didn't find any new likes or reached the last notification, stop
                if found_last_notification:
                    logger.info(f"🔄 Sync completed: reached last processed notification")
                    break
                
                # Safety check: if no notifications on this page, we've reached the end
                if len(notifications) == 0:
                    logger.info(f"🔄 Sync completed: no more notifications available")
                    break
                
                # Safety check: prevent infinite loops (max 100 pages)
                if page >= 100:
                    logger.warning(f"⚠️ Reached maximum page limit (100), stopping sync")
                    break
                
                page += 1
                await asyncio.sleep(0.2)  # Rate limiting
                
            except Exception as e:
                logger.error(f"❌ Error fetching notifications page {page}: {e}")
                break
        
        # STEP 2: Save new likes to database
        likes_saved = 0
        if all_likes:
            logger.info(f"💾 Saving {len(all_likes)} likes to database...")
            
            try:
                # Use upsert to handle duplicates
                insert_response = (
                    supabase.table("vinted_notifications")
                    .upsert(all_likes, on_conflict="vinted_user_id,notification_id,notification_type")
                    .execute()
                )
                likes_saved = len(insert_response.data) if insert_response.data else 0
                logger.info(f"✅ Saved {likes_saved} new likes")
                
            except Exception as e:
                logger.error(f"❌ Error saving likes: {e}")
        
        # STEP 2.5: Update the tracking record with the latest notification ID
        # This ensures we don't re-process the same notifications
        if latest_notification_id:
            try:
                logger.info(f"🔍 Preparing to save tracking record with notification_id: {latest_notification_id}")
                
                # First, delete the old tracking record if it exists
                delete_result = supabase.table("vinted_notifications").delete().eq("vinted_user_id", vinted_user_id).eq("user_id", user_id).eq("notification_type", "last_processed").execute()
                logger.info(f"🗑️ Deleted old tracking records: {len(delete_result.data) if delete_result.data else 0}")
                
                # Insert new tracking record with the latest notification ID
                tracking_data = {
                    "vinted_user_id": vinted_user_id,
                    "user_id": user_id,
                    "notification_id": latest_notification_id,
                    "notification_type": "last_processed",  # Special type for tracking
                    "liked_at": datetime.now(timezone.utc).isoformat(),
                    "item_id": "0",  # Placeholder for tracking record (not a real item)
                    "item_title": "Tracking Record",  # Placeholder title
                    "item_photo_url": "",  # Empty photo URL for tracking
                    "liker_username": "system",
                    "liker_user_id": "0",  # Placeholder user ID
                    "notification_body": f"Last processed notification: {latest_notification_id}",
                    "domain": domain
                }
                
                logger.info(f"🔍 Inserting tracking record: {tracking_data}")
                insert_result = supabase.table("vinted_notifications").insert(tracking_data).execute()
                logger.info(f"✅ Tracking record saved successfully: {insert_result.data}")
                logger.info(f"✅ Updated tracking record with latest notification ID: {latest_notification_id}")
                
            except Exception as e:
                logger.error(f"❌ Error updating tracking record: {e}")
                logger.error(f"❌ tracking_data was: {tracking_data if 'tracking_data' in locals() else 'Not defined'}")
        else:
            logger.warning("⚠️ No latest_notification_id found, tracking record not updated")
        
        # STEP 3: Get recent likes from database (always fresh)
        logger.info("📊 Fetching recent likes from database...")
        
        likes_response = (
            supabase.table("vinted_notifications")
            .select("item_id, item_title, item_photo_url, liker_username, liked_at, notification_body, link, domain")
            .eq("vinted_user_id", vinted_user_id)
            .eq("user_id", user_id)
            .eq("notification_type", "like")  # Only get actual likes, not tracking records
            .order("liked_at", desc=True)
            .limit(params.get("limit", 10))
            .execute()
        )
        
        recent_likes = []
        if likes_response.data:
            for like in likes_response.data:
                # Calculate time ago
                try:
                    liked_at = datetime.fromisoformat(like["liked_at"].replace("Z", "+00:00"))
                    time_ago = format_time_ago(int(liked_at.timestamp()))
                except:
                    time_ago = "Unknown time"
                
                # Build the full URL for redirect
                domain = like.get("domain", "www.vinted.it")
                link = like.get("link", "")
                full_url = f"https://{domain}{link}" if link else ""
                
                recent_likes.append({
                    "item_id": like["item_id"],
                    "item_title": like["item_title"],
                    "item_photo_url": like["item_photo_url"],
                    "actor_username": like["liker_username"],  # Keep compatibility
                    "liker_username": like["liker_username"],
                    "liked_at": like["liked_at"],
                    "occurred_at": like["liked_at"],  # Keep compatibility
                    "notification_body": like["notification_body"],
                    "time_ago": time_ago,
                    "link": link,
                    "full_url": full_url
                })
        
        logger.info(f"✅ Successfully synced and returned {len(recent_likes)} recent likes (saved {likes_saved} new)")
        
        # 🚀 SEND PROGRESSIVE UPDATE TO FRONTEND (Final page)
        try:
            from app.services.connection_manager import manager
            await manager.send_to_user_react(
                user_id,
                {
                    "type": "RECENT_LIKES_PAGE",
                    "workflow_key": "GET_RECENT_LIKES",
                    "page_number": 1,
                    "likes": recent_likes,
                    "pagination": {
                        "current_page": 1,
                        "total_pages": 1,
                        "is_last_page": True,
                        "total_loaded": len(recent_likes)
                    }
                }
            )
            logger.info(f"📤 Sent final page with {len(recent_likes)} likes to frontend")
        except Exception as e:
            logger.error(f"❌ Failed to send likes page to frontend: {e}")
        
        # Still return the traditional result for backward compatibility
        return {
            "recent_likes": recent_likes,
            "total_count": len(recent_likes),
            "likes_saved": likes_saved,
            "sync_completed": True
        }
        
    except Exception as e:
        logger.error(f"❌ Error in unified likes workflow: {e}")
        
        # Fallback: try to get existing likes from database
        try:
            likes_response = (
                supabase.table("vinted_notifications")
                .select("item_id, item_title, item_photo_url, liker_username, liked_at, notification_body, link, domain")
                .eq("vinted_user_id", vinted_user_id)
                .eq("user_id", user_id)
                .eq("notification_type", "like")  # Only get actual likes, not tracking records
                .order("liked_at", desc=True)
                .limit(params.get("limit", 10))
                .execute()
            )
            
            fallback_likes = []
            if likes_response.data:
                for like in likes_response.data:
                    try:
                        liked_at = datetime.fromisoformat(like["liked_at"].replace("Z", "+00:00"))
                        time_ago = format_time_ago(int(liked_at.timestamp()))
                    except:
                        time_ago = "Unknown time"
                    
                    # Build the full URL for redirect
                    domain = like.get("domain", "www.vinted.it")
                    link = like.get("link", "")
                    full_url = f"https://{domain}{link}" if link else ""
                    
                    fallback_likes.append({
                        "item_id": like["item_id"],
                        "item_title": like["item_title"],
                        "item_photo_url": like["item_photo_url"],
                        "actor_username": like["liker_username"],
                        "liker_username": like["liker_username"],
                        "liked_at": like["liked_at"],
                        "occurred_at": like["liked_at"],
                        "notification_body": like["notification_body"],
                        "time_ago": time_ago,
                        "link": link,
                        "full_url": full_url
                    })
            
            return {
                "recent_likes": fallback_likes,
                "total_count": len(fallback_likes),
                "likes_saved": 0,
                "sync_completed": False,
                "error": f"Sync failed but showing cached likes: {str(e)}"
            }
            
        except Exception as fallback_error:
            logger.error(f"❌ Fallback also failed: {fallback_error}")
            return {
                "recent_likes": [],
                "total_count": 0,
                "likes_saved": 0,
                "sync_completed": False,
                "error": str(e)
            }
