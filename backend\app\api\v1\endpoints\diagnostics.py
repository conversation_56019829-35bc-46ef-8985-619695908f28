import logging
from fastapi import APIRouter, Depends, HTTPException, Request, status
from app.services.connection_manager import manager
from app.core.security import get_user_id_from_token

router = APIRouter()
logger = logging.getLogger(__name__)

async def _auth_from_bearer(request: Request) -> str:
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated")
    token = auth_header.split(" ")[1]
    user_id = await get_user_id_from_token(token)
    if not user_id:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    return user_id

@router.get("/ws-status")
async def ws_status(user_id: str = Depends(_auth_from_bearer)):
    return {
        "user_id": user_id,
        "react_clients": len(manager.react_connections.get(user_id, [])),
        "extension_online": manager.is_extension_online(user_id),
        "extension_meta": {
            "has_vinted_user_info": bool(manager.get_extension_data(user_id, "vinted_user_info")),
            "last_login_check": manager.get_extension_data(user_id, "last_login_check"),
            "vinted_domain": manager.get_extension_data(user_id, "vinted_domain"),
        },
    }

@router.get("/ping")
async def ping():
    return {"ok": True}
