# app/api/v1/router.py
from fastapi import APIRouter
from app.api.v1.endpoints import billing, ws, likes, follow
from app.api.v1.endpoints import diagnostics

api_router = APIRouter()
api_router.include_router(ws.router, tags=["WebSocket"])
api_router.include_router(
    billing.router, prefix="/billing", tags=["Billing"]
)
api_router.include_router(
    likes.router, prefix="/likes", tags=["Likes"]
)
api_router.include_router(
    follow.router, prefix="/follow", tags=["Follow"]
)
api_router.include_router(
    diagnostics.router, prefix="/diagnostics", tags=["Diagnostics"]
)