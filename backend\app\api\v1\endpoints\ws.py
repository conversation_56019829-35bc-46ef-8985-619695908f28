import asyncio
import uuid
import logging
from datetime import datetime, timezone
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query, status
from typing import Optional

from app.core.security import get_user_id_from_token
from app.services.connection_manager import manager
from app.services.vinted_fetcher import jobs_in_progress
from app.services.vinted_workflows import WORKFLOW_MAP
from app.models.plans import get_user_permissions, PermissionDeniedError
from app.db.supabase_client import supabase

router = APIRouter()
logger = logging.getLogger(__name__)

# # --- Gestore Esecuzione Workflow ---
# async def run_workflow_and_respond(workflow_func, user_id, params, request_id, workflow_key):
#     params['request_id'] = request_id
#     try:
#         permissions = await get_user_permissions(user_id)
#         if not permissions["is_plan_active"]: raise PermissionDeniedError(f"Il piano {permissions['plan_name']} non è attivo.")
#         if not permissions["features"].get(workflow_key, False): raise PermissionDeniedError(f"Funzionalità '{workflow_key}' non disponibile per il tuo piano.")
        
#         limit_key = f"max_{workflow_key}_per_day"
#         limit = permissions["limits"].get(limit_key)
#         if limit is not None and limit != -1:
#             start_of_day = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0).isoformat()
#             count_response = supabase.table("usage_logs").select("id", count="exact").eq("user_id", user_id).eq("feature_key", workflow_key).gte("created_at", start_of_day).execute()
#             if count_response.count >= limit:
#                 raise PermissionDeniedError(f"Hai raggiunto il limite di {limit} utilizzi giornalieri per '{workflow_key}'.")
        
#         result_data = await workflow_func(user_id, params)
#         response = {"type": "WORKFLOW_RESULT", "request_id": request_id, "workflow_key": workflow_key, "status": "success", "data": result_data}
        
#         if limit is not None and limit != -1:
#             supabase.table("usage_logs").insert({"user_id": user_id, "feature_key": workflow_key}).execute()

#     except PermissionDeniedError as e:
#         response = {"type": "WORKFLOW_RESULT", "request_id": request_id, "workflow_key": workflow_key, "status": "failed", "error": str(e), "error_code": "PERMISSION_DENIED"}
#     except Exception as e: 
#         logging.exception(f"Errore workflow {request_id} ({workflow_key}) per {user_id}: {e}")
#         response = {"type": "WORKFLOW_RESULT", "request_id": request_id, "workflow_key": workflow_key, "status": "failed", "error": f"Errore imprevisto: {str(e)}"}
    
#     await manager.send_to_user_react(user_id, response)

# # --- Gestore Messaggi ---
# async def handle_message(user_id: str, client_type: str, data: dict):
#     message_type = data.get("type")

#     if client_type == "react" and message_type == "EXECUTE_WORKFLOW":
#         workflow_key, params, req_id = (
#             data.get("workflow_key"),
#             data.get("params", {}),
#             data.get("request_id", str(uuid.uuid4())),
#         )
#         if workflow_key in WORKFLOW_MAP:
#             asyncio.create_task(
#                 run_workflow_and_respond(
#                     WORKFLOW_MAP[workflow_key], user_id, params, req_id, workflow_key
#                 )
#             )
#         else:
#             await manager.send_to_user_react(
#                 user_id,
#                 {
#                     "type": "WORKFLOW_RESULT",
#                     "request_id": req_id,
#                     "status": "failed",
#                     "error": f"Workflow '{workflow_key}' sconosciuto.",
#                 },
#             )

#     elif client_type == "extension" and "job_id" in data:
#         future = jobs_in_progress.get(data.get("job_id"))
#         if future and not future.done():
#             if data.get("status") == "success":
#                 future.set_result(data)
#             else:
#                 future.set_exception(
#                     RuntimeError(data.get("error", "Errore estensione"))
#                 )

#     elif client_type == "extension" and message_type == "EXTENSION_VINTED_READY":
#         vinted_data = data.get("data", {})
#         if "domain" in vinted_data:
#             manager.update_extension_data(
#                 user_id, {"vinted_domain": vinted_data["domain"]}
#             )
#             logging.info(
#                 f"Dominio Vinted '{vinted_data['domain']}' salvato per utente {user_id}."
#             )

#     elif client_type == "extension" and message_type == "PING":
#         await manager.send_to_extension(user_id, {"type": "PONG"})


# @router.websocket("/ws")
# async def websocket_endpoint(websocket: WebSocket, token: Optional[str] = Query(None), client_type: Optional[str] = Query(None)):
#     user_id = await get_user_id_from_token(token)
#     if not user_id or client_type not in ["react", "extension"]:
#         await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
#         return
    
#     await manager.connect(websocket, user_id, client_type)
    
#     try:
#         while True:
#             data = await websocket.receive_json()
#             await handle_message(user_id, client_type, data)
#     except WebSocketDisconnect:
#         manager.disconnect(websocket, user_id, client_type)
#     except Exception as e:
#         logger.exception(f"❌ Errore WS per {user_id} ({client_type}): {e}")
#         manager.disconnect(websocket, user_id, client_type)

# @router.get("/ws-test")
# def test_route():
#     return {"hello": "world"}