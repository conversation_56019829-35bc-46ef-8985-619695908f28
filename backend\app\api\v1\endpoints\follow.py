# app/api/v1/endpoints/follow.py
from datetime import date, datetime, timezone
from typing import Dict, List, Optional, Union
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field
import logging
import uuid
from app.core.security import get_current_user_id
from app.db.supabase_client import supabase
from app.models.plans import get_user_permissions, check_daily_limits, PermissionDeniedError
from app.services.connection_manager import manager
from app.services.rate_limiter import rate_limiter, ActionType

logger = logging.getLogger(__name__)
router = APIRouter()

# Request/Response Models
class FollowUserRequest(BaseModel):
    vinted_user_id: str = Field(..., description="Vinted user ID to follow")
    vinted_username: str = Field(..., description="Vinted username")
    vinted_display_name: Optional[str] = Field(None, description="Vinted display name")
    vinted_avatar_url: Optional[str] = Field(None, description="Vinted avatar URL")

class UnfollowUserRequest(BaseModel):
    vinted_user_id: str = Field(..., description="Vinted user ID to unfollow")

class BulkFollowRequest(BaseModel):
    users: List[FollowUserRequest] = Field(..., description="List of users to follow", max_items=50)

class FollowedUserResponse(BaseModel):
    id: str
    vinted_user_id: str
    vinted_username: str
    vinted_display_name: Optional[str]
    vinted_avatar_url: Optional[str]
    is_following: bool
    followed_at: datetime
    unfollowed_at: Optional[datetime]
    last_updated: datetime

class FollowUsageStatsResponse(BaseModel):
    date: date
    follow_count: int
    unfollow_count: int
    total_actions: int
    success_rate: float
    daily_limit: int
    remaining_actions: int

class FollowActivityLogResponse(BaseModel):
    id: str
    vinted_user_id: str
    vinted_username: str
    action_type: str
    success: bool
    error_message: Optional[str]
    execution_time_ms: Optional[int]
    created_at: datetime
    triggered_by: str
    request_source: str

async def log_follow_activity(
    user_id: str,
    vinted_user_id: str, 
    vinted_username: str,
    action_type: str,
    success: bool = False,
    error_message: Optional[str] = None,
    execution_time_ms: Optional[int] = None,
    triggered_by: str = "manual",
    request_source: str = "webapp"
):
    """Log follow/unfollow activity for tracking and analytics"""
    try:
        result = supabase.table("follow_activity_logs").insert({
            "user_id": user_id,
            "vinted_user_id": vinted_user_id,
            "vinted_username": vinted_username,
            "action_type": action_type,
            "success": success,
            "error_message": error_message,
            "execution_time_ms": execution_time_ms,
            "triggered_by": triggered_by,
            "request_source": request_source
        }).execute()
        
        # Update usage statistics using RPC function
        try:
            supabase.rpc("update_follow_usage_stats", {
                "p_user_id": user_id,
                "p_action_type": action_type,
                "p_success": success
            }).execute()
        except Exception as rpc_error:
            # Log warning but don't fail if usage stats function doesn't exist yet
            logger.warning(f"Usage stats function not available: {rpc_error}")
        
    except Exception as e:
        # Log warning but don't fail the main operation if activity logging fails
        logger.warning(f"Failed to log follow activity: {e}")

async def execute_follow_action(
    user_id: str, 
    vinted_user_id: str, 
    vinted_username: str,
    action_type: str,
    display_name: Optional[str] = None,
    avatar_url: Optional[str] = None
) -> Dict[str, Union[bool, str]]:
    """Execute follow/unfollow action via extension"""
    try:
        # Check if extension is connected
        if not manager.is_extension_online(user_id):
            return {
                "success": False,
                "error": "Extension must be connected to perform follow actions",
                "error_code": "EXTENSION_OFFLINE"
            }
        
        # Generate request ID
        request_id = str(uuid.uuid4())
        
        # Send action to extension
        await manager.send_to_user_extension(user_id, {
            "type": "FOLLOW_ACTION",
            "request_id": request_id,
            "action": action_type,
            "vinted_user_id": vinted_user_id,
            "vinted_username": vinted_username
        })
        
        # Wait for response from extension (with timeout)
        import asyncio
        try:
            # Wait for extension response for up to 30 seconds
            response = await asyncio.wait_for(
                manager.wait_for_extension_response(user_id, request_id),
                timeout=30.0
            )
            
            success = response.get("success", False)
            error = response.get("error")
            execution_time = response.get("execution_time_ms")
            
            # Update database if action was successful
            if success:
                if action_type == "follow":
                    # Insert or update followed_users record
                    supabase.table("followed_users").upsert({
                        "user_id": user_id,
                        "vinted_user_id": vinted_user_id,
                        "vinted_username": vinted_username,
                        "vinted_display_name": display_name,
                        "vinted_avatar_url": avatar_url,
                        "is_following": True,
                        "followed_at": datetime.now(timezone.utc).isoformat(),
                        "unfollowed_at": None
                    }).execute()
                    
                elif action_type == "unfollow":
                    # Update existing record to mark as unfollowed
                    supabase.table("followed_users").update({
                        "is_following": False,
                        "unfollowed_at": datetime.now(timezone.utc).isoformat()
                    }).eq("user_id", user_id).eq("vinted_user_id", vinted_user_id).execute()
            
            # Log the activity
            await log_follow_activity(
                user_id=user_id,
                vinted_user_id=vinted_user_id,
                vinted_username=vinted_username,
                action_type=action_type,
                success=success,
                error_message=error,
                execution_time_ms=execution_time,
                request_source="webapp"
            )
            
            return {
                "success": success,
                "error": error,
                "execution_time_ms": execution_time
            }
            
        except asyncio.TimeoutError:
            error_msg = f"Extension timeout: {action_type} action took too long"
            await log_follow_activity(
                user_id=user_id,
                vinted_user_id=vinted_user_id,
                vinted_username=vinted_username,
                action_type=action_type,
                success=False,
                error_message=error_msg,
                request_source="webapp"
            )
            return {
                "success": False,
                "error": error_msg,
                "error_code": "TIMEOUT"
            }
            
    except Exception as e:
        error_msg = f"Failed to execute {action_type} action: {str(e)}"
        await log_follow_activity(
            user_id=user_id,
            vinted_user_id=vinted_user_id,
            vinted_username=vinted_username,
            action_type=action_type,
            success=False,
            error_message=error_msg,
            request_source="webapp"
        )
        return {
            "success": False,
            "error": error_msg,
            "error_code": "EXECUTION_ERROR"
        }

@router.post("/follow", response_model=Dict[str, Union[bool, str]])
async def follow_user(
    request: FollowUserRequest,
    user_id: str = Depends(get_current_user_id)
):
    """Follow a Vinted user with advanced rate limiting"""
    start_time = datetime.now(timezone.utc)
    
    try:
        # Check rate limits using the advanced rate limiter
        allowed, error_message, usage_stats = await rate_limiter.check_rate_limit(
            user_id, ActionType.FOLLOW
        )
        
        if not allowed:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=error_message
            )
        
        # Execute follow action
        result = await execute_follow_action(
            user_id=user_id,
            vinted_user_id=request.vinted_user_id,
            vinted_username=request.vinted_username,
            action_type="follow",
            display_name=request.vinted_display_name,
            avatar_url=request.vinted_avatar_url
        )
        
        execution_time_ms = int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000)
        
        # Record the action in rate limiter
        await rate_limiter.record_action(
            user_id=user_id,
            action_type=ActionType.FOLLOW,
            success=result["success"],
            vinted_user_id=request.vinted_user_id,
            error_message=result.get("error") if not result["success"] else None
        )
        
        if not result["success"]:
            error_code = result.get("error_code", "UNKNOWN_ERROR")
            if error_code == "EXTENSION_OFFLINE":
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail=result["error"]
                )
            elif error_code == "TIMEOUT":
                raise HTTPException(
                    status_code=status.HTTP_408_REQUEST_TIMEOUT,
                    detail=result["error"]
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result["error"]
                )
        
        return {
            "success": True,
            "message": f"Successfully followed user {request.vinted_username}",
            "execution_time_ms": execution_time_ms,
            "remaining_daily_actions": max(0, usage_stats.remaining_today - 1)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error following user {request.vinted_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during follow operation"
        )

@router.post("/unfollow", response_model=Dict[str, Union[bool, str]])
async def unfollow_user(
    request: UnfollowUserRequest,
    user_id: str = Depends(get_current_user_id)
):
    """Unfollow a Vinted user"""
    try:
        # Check permissions
        permissions = await get_user_permissions(user_id)
        if not permissions.get("UNFOLLOW_USER", False):
            raise PermissionDeniedError("Your plan doesn't support unfollow functionality")
        
        # Check daily limits
        try:
            await check_daily_limits(user_id, "UNFOLLOW_USER")
        except PermissionDeniedError as e:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=str(e)
            )
        
        # Get user info from database
        followed_user = supabase.table("followed_users").select("*").eq(
            "user_id", user_id
        ).eq("vinted_user_id", request.vinted_user_id).eq(
            "is_following", True
        ).execute()
        
        if not followed_user.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User is not currently followed"
            )
        
        user_info = followed_user.data[0]
        
        # Execute unfollow action
        result = await execute_follow_action(
            user_id=user_id,
            vinted_user_id=request.vinted_user_id,
            vinted_username=user_info["vinted_username"],
            action_type="unfollow"
        )
        
        if not result["success"]:
            error_code = result.get("error_code", "UNKNOWN_ERROR")
            if error_code == "EXTENSION_OFFLINE":
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail=result["error"]
                )
            elif error_code == "TIMEOUT":
                raise HTTPException(
                    status_code=status.HTTP_408_REQUEST_TIMEOUT,
                    detail=result["error"]
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result["error"]
                )
        
        return {
            "success": True,
            "message": f"Successfully unfollowed user {user_info['vinted_username']}",
            "execution_time_ms": result.get("execution_time_ms")
        }
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error unfollowing user {request.vinted_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during unfollow operation"
        )

@router.get("/following", response_model=List[FollowedUserResponse])
async def get_following_users(
    is_following: Optional[bool] = Query(None, description="Filter by following status"),
    limit: int = Query(50, ge=1, le=200, description="Number of users to return"),
    offset: int = Query(0, ge=0, description="Number of users to skip"),
    user_id: str = Depends(get_current_user_id)
):
    """Get list of followed/unfollowed users"""
    try:
        query = supabase.table("followed_users").select("*").eq("user_id", user_id)
        
        if is_following is not None:
            query = query.eq("is_following", is_following)
        
        result = query.order("followed_at", desc=True).range(offset, offset + limit - 1).execute()
        
        return [FollowedUserResponse(**user) for user in result.data]
        
    except Exception as e:
        logger.error(f"Error getting following users for {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve following users"
        )

@router.get("/usage-stats", response_model=FollowUsageStatsResponse)
async def get_follow_usage_stats(
    date: Optional[date] = Query(None, description="Date to get stats for (default: today)"),
    user_id: str = Depends(get_current_user_id)
):
    """Get follow/unfollow usage statistics for a specific date"""
    try:
        target_date = date or datetime.now().date()
        
        # Get usage stats (with fallback if table doesn't exist)
        try:
            stats_result = supabase.table("follow_usage_stats").select("*").eq(
                "user_id", user_id
            ).eq("date", target_date).execute()
            stats_data = stats_result.data[0] if stats_result.data else None
        except Exception as e:
            logger.warning(f"Usage stats table not available: {e}")
            stats_data = None
        
        # Get user permissions for daily limits
        permissions = await get_user_permissions(user_id)
        daily_limit = permissions.get("max_FOLLOW_USER_per_day", 0) + permissions.get("max_UNFOLLOW_USER_per_day", 0)
        
        if stats_data:
            return FollowUsageStatsResponse(
                date=target_date,
                follow_count=stats_data["follow_count"],
                unfollow_count=stats_data["unfollow_count"],
                total_actions=stats_data["total_actions"],
                success_rate=float(stats_data["success_rate"]),
                daily_limit=daily_limit,
                remaining_actions=max(0, daily_limit - stats_data["total_actions"])
            )
        else:
            return FollowUsageStatsResponse(
                date=target_date,
                follow_count=0,
                unfollow_count=0,
                total_actions=0,
                success_rate=0.0,
                daily_limit=daily_limit,
                remaining_actions=daily_limit
            )
            
    except Exception as e:
        logger.error(f"Error getting usage stats for {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve usage statistics"
        )

@router.get("/activity", response_model=List[FollowActivityLogResponse])
async def get_follow_activity(
    days: int = Query(7, ge=1, le=30, description="Number of days of activity to retrieve"),
    action_type: Optional[str] = Query(None, description="Filter by action type (follow/unfollow)"),
    limit: int = Query(100, ge=1, le=500, description="Number of activities to return"),
    offset: int = Query(0, ge=0, description="Number of activities to skip"),
    user_id: str = Depends(get_current_user_id)
):
    """Get follow/unfollow activity history"""
    try:
        from datetime import timedelta
        
        since_date = datetime.now() - timedelta(days=days)
        
        # Try to get activity logs (with fallback if table doesn't exist)
        try:
            query = supabase.table("follow_activity_logs").select("*").eq(
                "user_id", user_id
            ).gte("created_at", since_date.isoformat())
            
            if action_type:
                if action_type not in ["follow", "unfollow"]:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="action_type must be 'follow' or 'unfollow'"
                    )
                query = query.eq("action_type", action_type)
            
            result = query.order("created_at", desc=True).range(offset, offset + limit - 1).execute()
            
            return [FollowActivityLogResponse(**activity) for activity in result.data]
            
        except Exception as e:
            if "does not exist" in str(e) or "Not Found" in str(e):
                logger.warning(f"Activity logs table not available: {e}")
                return []  # Return empty list if table doesn't exist yet
            else:
                raise e
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting activity for {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve activity history"
        )

@router.post("/bulk-follow", response_model=Dict[str, Union[bool, List[Dict]]])
async def bulk_follow_users(
    request: BulkFollowRequest,
    user_id: str = Depends(get_current_user_id)
):
    """Follow multiple users in bulk"""
    try:
        # Check permissions
        permissions = await get_user_permissions(user_id)
        if not permissions.get("FOLLOW_USER", False):
            raise PermissionDeniedError("Your plan doesn't support follow functionality")
        
        # Check if extension is connected
        if not manager.is_extension_online(user_id):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Extension must be connected to perform bulk follow operations"
            )
        
        results = []
        successful_follows = 0
        
        for follow_request in request.users:
            try:
                # Check daily limits before each follow
                await check_daily_limits(user_id, "FOLLOW_USER")
                
                result = await execute_follow_action(
                    user_id=user_id,
                    vinted_user_id=follow_request.vinted_user_id,
                    vinted_username=follow_request.vinted_username,
                    action_type="follow",
                    display_name=follow_request.vinted_display_name,
                    avatar_url=follow_request.vinted_avatar_url
                )
                
                if result["success"]:
                    successful_follows += 1
                
                results.append({
                    "vinted_user_id": follow_request.vinted_user_id,
                    "vinted_username": follow_request.vinted_username,
                    "success": result["success"],
                    "error": result.get("error"),
                    "execution_time_ms": result.get("execution_time_ms")
                })
                
                # Add delay between requests to avoid rate limiting
                import asyncio
                await asyncio.sleep(1)
                
            except PermissionDeniedError as e:
                # If daily limit is reached, stop processing
                results.append({
                    "vinted_user_id": follow_request.vinted_user_id,
                    "vinted_username": follow_request.vinted_username,
                    "success": False,
                    "error": str(e),
                    "error_code": "DAILY_LIMIT_REACHED"
                })
                break
            except Exception as e:
                results.append({
                    "vinted_user_id": follow_request.vinted_user_id,
                    "vinted_username": follow_request.vinted_username,
                    "success": False,
                    "error": str(e)
                })
        
        return {
            "success": True,
            "message": f"Processed {len(results)} follow requests, {successful_follows} successful",
            "successful_follows": successful_follows,
            "total_requests": len(request.users),
            "results": results
        }
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in bulk_follow: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal error: {str(e)}"
        )

@router.get("/enhanced-usage-stats", response_model=Dict[str, Union[str, int, float, Dict]])
async def get_enhanced_usage_stats(user_id: str = Depends(get_current_user_id)):
    """Get comprehensive usage statistics with rate limiting information"""
    try:
        usage_summary = await rate_limiter.get_user_usage_summary(user_id)
        return usage_summary
        
    except Exception as e:
        logger.error(f"Error getting enhanced usage stats for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal error: {str(e)}"
        )
        logger.error(f"Error in bulk follow for {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during bulk follow operation"
        )
