"""
Enhanced rate limiting and feature usage tracking for follow/unfollow operations.
Implements Vinted API constraints and daily limits with proper error handling.
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, List
from dataclasses import dataclass
from enum import Enum

from app.db.supabase_client import supabase
from app.models.plans import get_user_permissions

logger = logging.getLogger(__name__)

class ActionType(Enum):
    FOLLOW = "follow"
    UNFOLLOW = "unfollow"
    BULK_FOLLOW = "bulk_follow"
    AUTOMATION_FOLLOW = "automation_follow"

@dataclass
class RateLimitRule:
    max_actions_per_hour: int
    max_actions_per_day: int
    min_interval_seconds: int
    burst_limit: int  # Maximum actions in rapid succession
    burst_window_seconds: int  # Time window for burst detection

@dataclass
class UsageStats:
    total_today: int
    total_this_hour: int
    recent_actions: List[datetime]
    daily_limit: int
    hourly_limit: int
    remaining_today: int
    remaining_this_hour: int
    next_reset_time: datetime

class VintedRateLimiter:
    """
    Advanced rate limiter that respects Vinted's API constraints and user plan limits.
    Implements multiple layers of rate limiting:
    1. Vinted API limits (anti-spam protection)
    2. User plan limits (subscription tiers)
    3. Burst protection (prevent rapid-fire requests)
    4. Cool-down periods (after rate limit hits)
    """
    
    def __init__(self):
        # Vinted API rate limiting rules based on observed behavior
        self.vinted_limits = {
            ActionType.FOLLOW: RateLimitRule(
                max_actions_per_hour=50,  # Conservative estimate
                max_actions_per_day=200,  # Vinted's daily follow limit
                min_interval_seconds=3,   # Minimum 3 seconds between follows
                burst_limit=5,           # Max 5 follows in burst
                burst_window_seconds=60  # Within 1 minute window
            ),
            ActionType.UNFOLLOW: RateLimitRule(
                max_actions_per_hour=100, # Unfollows are less restricted
                max_actions_per_day=500,  # Higher limit for unfollows
                min_interval_seconds=2,   # Faster unfollow interval
                burst_limit=10,          # More burst unfollows allowed
                burst_window_seconds=60
            ),
            ActionType.AUTOMATION_FOLLOW: RateLimitRule(
                max_actions_per_hour=20,  # Slower for automation
                max_actions_per_day=100,  # Conservative for automation
                min_interval_seconds=10,  # Slower interval for automation
                burst_limit=3,           # Very limited burst
                burst_window_seconds=120 # Longer burst window
            )
        }
        
        # Cache for user rate limiting data
        self._user_cache: Dict[str, Dict[str, any]] = {}
        self._cache_ttl_seconds = 300  # 5 minutes cache TTL
    
    async def check_rate_limit(self, user_id: str, action_type: ActionType) -> tuple[bool, Optional[str], UsageStats]:
        """
        Check if user can perform the action based on rate limits.
        
        Returns:
            (allowed: bool, error_message: Optional[str], usage_stats: UsageStats)
        """
        try:
            # Get user permissions and plan limits
            permissions = await get_user_permissions(user_id)
            if not permissions["is_plan_active"]:
                return False, "Plan is not active", self._empty_usage_stats()
            
            # Check feature permission
            feature_key = f"{action_type.value.upper()}_USER"
            if not permissions["features"].get(feature_key, False):
                return False, f"Feature {action_type.value} not available in your plan", self._empty_usage_stats()
            
            # Get usage statistics
            usage_stats = await self._get_usage_stats(user_id, action_type, permissions)
            
            # Check daily limit from user plan
            daily_limit = permissions["limits"].get(f"max_{feature_key}_per_day", 0)
            if usage_stats.total_today >= daily_limit:
                return False, f"Daily {action_type.value} limit reached ({daily_limit})", usage_stats
            
            # Check Vinted API limits
            vinted_rule = self.vinted_limits.get(action_type)
            if vinted_rule:
                # Check hourly Vinted limit
                if usage_stats.total_this_hour >= vinted_rule.max_actions_per_hour:
                    return False, f"Hourly {action_type.value} limit reached. Please wait.", usage_stats
                
                # Check daily Vinted limit
                if usage_stats.total_today >= vinted_rule.max_actions_per_day:
                    return False, f"Daily Vinted {action_type.value} limit reached", usage_stats
                
                # Check minimum interval
                if usage_stats.recent_actions:
                    last_action = max(usage_stats.recent_actions)
                    time_since_last = (datetime.now(timezone.utc) - last_action).total_seconds()
                    if time_since_last < vinted_rule.min_interval_seconds:
                        wait_time = vinted_rule.min_interval_seconds - time_since_last
                        return False, f"Please wait {wait_time:.1f} seconds before next {action_type.value}", usage_stats
                
                # Check burst protection
                recent_burst_actions = [
                    action for action in usage_stats.recent_actions
                    if (datetime.now(timezone.utc) - action).total_seconds() <= vinted_rule.burst_window_seconds
                ]
                if len(recent_burst_actions) >= vinted_rule.burst_limit:
                    return False, f"Too many {action_type.value} actions in short time. Please slow down.", usage_stats
            
            return True, None, usage_stats
            
        except Exception as e:
            logger.error(f"Rate limit check error for user {user_id}: {e}")
            return False, f"Rate limit check failed: {str(e)}", self._empty_usage_stats()
    
    async def record_action(self, user_id: str, action_type: ActionType, success: bool, 
                          vinted_user_id: str = None, error_message: str = None) -> bool:
        """
        Record an action in the usage tracking system.
        
        Returns True if recorded successfully, False otherwise.
        """
        try:
            now = datetime.now(timezone.utc)
            
            # Record in follow_activity_logs if table exists
            try:
                activity_data = {
                    "user_id": user_id,
                    "action_type": action_type.value,
                    "vinted_user_id": vinted_user_id,
                    "success": success,
                    "error_message": error_message,
                    "created_at": now.isoformat()
                }
                
                # Try to insert into activity logs
                supabase.table("follow_activity_logs").insert(activity_data).execute()
                logger.info(f"Recorded {action_type.value} action for user {user_id}: {'success' if success else 'failed'}")
                
            except Exception as e:
                # If table doesn't exist, continue without logging activity
                logger.warning(f"Could not log activity (table may not exist): {e}")
            
            # Update usage stats if action was successful
            if success:
                await self._update_usage_stats(user_id, action_type)
            
            # Clear cache for user to ensure fresh data
            if user_id in self._user_cache:
                del self._user_cache[user_id]
            
            return True
            
        except Exception as e:
            logger.error(f"Error recording action for user {user_id}: {e}")
            return False
    
    async def get_user_usage_summary(self, user_id: str) -> Dict[str, any]:
        """Get comprehensive usage summary for a user."""
        try:
            permissions = await get_user_permissions(user_id)
            
            summary = {
                "user_id": user_id,
                "plan_id": permissions["plan_id"],
                "plan_name": permissions["plan_name"],
                "is_plan_active": permissions["is_plan_active"],
                "usage_by_action": {},
                "limits_by_action": {},
                "next_reset_time": datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            }
            
            # Get usage for each action type
            for action_type in [ActionType.FOLLOW, ActionType.UNFOLLOW]:
                usage_stats = await self._get_usage_stats(user_id, action_type, permissions)
                feature_key = f"{action_type.value.upper()}_USER"
                daily_limit = permissions["limits"].get(f"max_{feature_key}_per_day", 0)
                
                summary["usage_by_action"][action_type.value] = {
                    "total_today": usage_stats.total_today,
                    "total_this_hour": usage_stats.total_this_hour,
                    "daily_limit": daily_limit,
                    "remaining_today": max(0, daily_limit - usage_stats.total_today),
                    "percentage_used": (usage_stats.total_today / daily_limit * 100) if daily_limit > 0 else 0
                }
                
                summary["limits_by_action"][action_type.value] = {
                    "daily_limit": daily_limit,
                    "vinted_daily_limit": self.vinted_limits[action_type].max_actions_per_day,
                    "vinted_hourly_limit": self.vinted_limits[action_type].max_actions_per_hour,
                    "min_interval_seconds": self.vinted_limits[action_type].min_interval_seconds
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting usage summary for user {user_id}: {e}")
            return {"error": str(e)}
    
    async def _get_usage_stats(self, user_id: str, action_type: ActionType, permissions: dict) -> UsageStats:
        """Get current usage statistics for a user and action type."""
        try:
            now = datetime.now(timezone.utc)
            today = now.date()
            current_hour = now.replace(minute=0, second=0, microsecond=0)
            
            # Try to get from follow_usage_stats table first
            try:
                usage_response = (
                    supabase.table("follow_usage_stats")
                    .select("*")
                    .eq("user_id", user_id)
                    .eq("date", today.isoformat())
                    .single()
                    .execute()
                )
                
                if usage_response.data:
                    usage_data = usage_response.data
                    total_today = usage_data.get(f"{action_type.value}_count", 0)
                else:
                    total_today = 0
                    
            except Exception:
                # Fallback to activity logs count
                total_today = await self._count_actions_from_logs(user_id, action_type, today)
            
            # Get hourly count from activity logs
            total_this_hour = await self._count_actions_from_logs(user_id, action_type, current_hour, True)
            
            # Get recent actions for interval checking
            recent_actions = await self._get_recent_actions(user_id, action_type)
            
            # Calculate limits
            feature_key = f"{action_type.value.upper()}_USER"
            daily_limit = permissions["limits"].get(f"max_{feature_key}_per_day", 0)
            vinted_rule = self.vinted_limits.get(action_type)
            hourly_limit = vinted_rule.max_actions_per_hour if vinted_rule else 0
            
            return UsageStats(
                total_today=total_today,
                total_this_hour=total_this_hour,
                recent_actions=recent_actions,
                daily_limit=daily_limit,
                hourly_limit=hourly_limit,
                remaining_today=max(0, daily_limit - total_today),
                remaining_this_hour=max(0, hourly_limit - total_this_hour),
                next_reset_time=now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            )
            
        except Exception as e:
            logger.error(f"Error getting usage stats for user {user_id}: {e}")
            return self._empty_usage_stats()
    
    async def _count_actions_from_logs(self, user_id: str, action_type: ActionType, 
                                     since_time, is_hourly: bool = False) -> int:
        """Count successful actions from activity logs."""
        try:
            if is_hourly:
                # Count from specific hour
                end_time = since_time + timedelta(hours=1)
                response = (
                    supabase.table("follow_activity_logs")
                    .select("id", count="exact")
                    .eq("user_id", user_id)
                    .eq("action_type", action_type.value)
                    .eq("success", True)
                    .gte("created_at", since_time.isoformat())
                    .lt("created_at", end_time.isoformat())
                    .execute()
                )
            else:
                # Count from date (since_time should be a date object)
                response = (
                    supabase.table("follow_activity_logs")
                    .select("id", count="exact")
                    .eq("user_id", user_id)
                    .eq("action_type", action_type.value)
                    .eq("success", True)
                    .gte("created_at", f"{since_time}T00:00:00Z")
                    .lt("created_at", f"{since_time}T23:59:59Z")
                    .execute()
                )
            
            return response.count if response.count is not None else 0
            
        except Exception:
            # If table doesn't exist, return 0
            return 0
    
    async def _get_recent_actions(self, user_id: str, action_type: ActionType) -> List[datetime]:
        """Get timestamps of recent actions for interval checking."""
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=10)  # Last 10 minutes
            
            response = (
                supabase.table("follow_activity_logs")
                .select("created_at")
                .eq("user_id", user_id)
                .eq("action_type", action_type.value)
                .eq("success", True)
                .gte("created_at", cutoff_time.isoformat())
                .order("created_at", desc=True)
                .limit(20)  # Get last 20 actions
                .execute()
            )
            
            if response.data:
                return [datetime.fromisoformat(row["created_at"].replace("Z", "+00:00")) for row in response.data]
            return []
            
        except Exception:
            return []
    
    async def _update_usage_stats(self, user_id: str, action_type: ActionType):
        """Update usage statistics in the database."""
        try:
            today = datetime.now(timezone.utc).date()
            
            # Try to use RPC function if available
            try:
                supabase.rpc("update_follow_usage_stats", {
                    "p_user_id": user_id,
                    "p_action_type": action_type.value,
                    "p_success": True
                }).execute()
                
            except Exception:
                # Fallback to manual upsert if RPC doesn't exist
                # This is a simplified version - in production you'd want proper upsert logic
                logger.warning(f"RPC function not available, skipping usage stats update for user {user_id}")
                pass
                
        except Exception as e:
            logger.error(f"Error updating usage stats for user {user_id}: {e}")
    
    def _empty_usage_stats(self) -> UsageStats:
        """Return empty usage stats for error cases."""
        return UsageStats(
            total_today=0,
            total_this_hour=0,
            recent_actions=[],
            daily_limit=0,
            hourly_limit=0,
            remaining_today=0,
            remaining_this_hour=0,
            next_reset_time=datetime.now(timezone.utc) + timedelta(days=1)
        )

# Global rate limiter instance
rate_limiter = VintedRateLimiter()
