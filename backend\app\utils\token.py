import re
from app.services.vinted_fetcher import perform_fetch_via_extension


async def _get_vinted_csrf_token(user_id: str, domain: str) -> str:
    """
    Gets a CSRF token from <PERSON><PERSON> by visiting the items/new page and extracting the token.
    
    Args:
        user_id: The user ID
        domain: The Vinted domain (e.g., "www.vinted.it")
        
    Returns:
        The CSRF token as a string
        
    Raises:
        RuntimeError: If the token cannot be extracted
    """
    try:
        # Visit the items/new page to get a fresh CSRF token
        response = await perform_fetch_via_extension(
            user_id, 
            {
                "url": f"https://{domain}/items/new",
                "method": "GET"
            }
        )
        
        if response.get("http_status_code", 500) >= 400:
            raise RuntimeError(f"Failed to fetch items/new page: HTTP {response.get('http_status_code')}")
        
        # Extract CSRF token from the response body
        response_body = response.get("raw_response_body", "")
        
        # Look for CSRF_TOKEN in the page content
        # Pattern: CSRF_TOKEN[^0-9A-Za-z]*([0-9a-fA-F]{8}(?:-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12})
        csrf_match = re.search(
            r'CSRF_TOKEN[^0-9A-Za-z]*([0-9a-fA-F]{8}(?:-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12})',
            response_body
        )
        
        if not csrf_match:
            raise RuntimeError("Could not extract CSRF token from Vinted page")
        
        csrf_token = csrf_match.group(1)
        return csrf_token
        
    except Exception as e:
        raise RuntimeError(f"Error getting CSRF token: {str(e)}")
