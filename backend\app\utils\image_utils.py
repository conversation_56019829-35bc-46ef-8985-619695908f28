# image_utils.py
import random
import io
from PIL import Image, ImageOps, ImageEnhance


def add_noise_to_color(color_tuple, noise_level=15):
    """Aggiunge un disturbo casuale a un colore RGB."""
    def noise():
        return random.randint(-noise_level, noise_level)
    return tuple(max(0, min(255, c + noise())) for c in color_tuple)


def get_dominant_border_color(img: Image.Image, sample_size=5) -> tuple:
    """Extracts the most common color from the image borders."""
    width, height = img.size
    border_pixels = []
    # Campiona pixel dai bordi
    for x in range(width):
        for y in range(sample_size):
            border_pixels.append(img.getpixel((x, y)))
            border_pixels.append(img.getpixel((x, height - 1 - y)))
    for y in range(height):
        for x in range(sample_size):
            border_pixels.append(img.getpixel((x, y)))
            border_pixels.append(img.getpixel((width - 1 - x, y)))

    # Trova il colore più frequente
    color_counts = {}
    for pixel in border_pixels:
        # Ignora la trasparenza se presente
        rgb_pixel = pixel[:3] if len(pixel) == 4 else pixel
        color_counts[rgb_pixel] = color_counts.get(rgb_pixel, 0) + 1

    if not color_counts:
        return (255, 255, 255)  # Fallback su bianco

    dominant_color = max(color_counts, key=color_counts.get)
    return dominant_color


def alter_photo(image_bytes: bytes) -> bytes:
    """
    Simple fallback function for image alteration.
    Returns the original image bytes unchanged.
    """
    return image_bytes
