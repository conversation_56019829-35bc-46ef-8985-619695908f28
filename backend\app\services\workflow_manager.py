# app/services/workflow_manager.py
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Callable
from enum import Enum
from dataclasses import dataclass, field

from app.db.supabase_client import supabase
from app.models.plans import get_user_permissions, PermissionDeniedError
from app.services.connection_manager import manager
from app.services.vinted_workflows import WORKFLOW_MAP

logger = logging.getLogger(__name__)


class WorkflowType(Enum):
    """Types of workflows supported by the manager."""
    INSTANT = "instant"      # One-shot workflows that return immediately 
    PERSISTENT = "persistent"  # Long-running background jobs


class BackgroundJobType(Enum):
    """Types of background jobs that can run persistently."""
    REPOST_AUTOMATION = "repost_automation"
    FOLLOW_AUTOMATION = "follow_automation"


@dataclass
class WorkflowState:
    """State of a running workflow."""
    workflow_id: str
    user_id: str
    workflow_key: str
    workflow_type: WorkflowType
    status: str
    params: Dict[str, Any]
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    progress: Optional[Dict[str, Any]] = None
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    task: Optional[asyncio.Task] = None
    request_id: Optional[str] = None


class WorkflowManager:
    """
    Unified controller for all workflows (instant + persistent).
    Replaces the scattered logic across main.py, workflow_controller.py, and background_tasks.py
    """
    
    def __init__(self):
        # All active workflows (instant + persistent)
        self.workflows: Dict[str, WorkflowState] = {}
        
        # User workflow tracking (user_id -> {workflow_key -> workflow_id})
        self.user_workflows: Dict[str, Dict[str, str]] = {}
        
        # Background jobs tracking (separate from workflows)
        self.running_background_jobs: Dict[str, asyncio.Task] = {}
        self.background_job_states: Dict[str, Dict[str, Any]] = {}
        
        # Connection manager for WebSocket notifications
        self.connection_manager = manager
        
        # Workflow type definitions
        self.workflow_types = {
            # Instant workflows - eseguiti immediatamente e ritornano risposta
            "GET_USER_PRODUCTS": WorkflowType.INSTANT,
    
            "CHECK_VINTED_LOGIN_STATUS": WorkflowType.INSTANT,
            "GET_VINTED_STATS": WorkflowType.INSTANT,
            "REPOST_ITEM": WorkflowType.INSTANT,
            "DELETE_ITEM": WorkflowType.INSTANT,
            "GET_PLAN_AND_USAGE": WorkflowType.INSTANT,
            "GET_VINTED_OFFERS": WorkflowType.INSTANT,
            "ACCEPT_VINTED_OFFER": WorkflowType.INSTANT,
            "DECLINE_VINTED_OFFER": WorkflowType.INSTANT,
            "COUNTER_VINTED_OFFER": WorkflowType.INSTANT,
            "GET_FOLLOWER_STATS": WorkflowType.INSTANT,
            "GET_USER_PROFILE": WorkflowType.INSTANT,
            "GET_VINTED_ORDERS": WorkflowType.INSTANT,
            "SYNC_VINTED_NOTIFICATIONS": WorkflowType.INSTANT,
            "GET_RECENT_LIKES": WorkflowType.INSTANT,
            "GET_UNREAD_MESSAGES": WorkflowType.INSTANT,
            "GET_BACKGROUND_TASKS": WorkflowType.INSTANT,
            "GET_BACKGROUND_TASK_STATUS": WorkflowType.INSTANT,
            "CANCEL_BACKGROUND_TASK": WorkflowType.INSTANT,
            "GET_BACKGROUND_TASK_STATS": WorkflowType.INSTANT,
            "GET_AUTO_MESSAGE_SETTINGS": WorkflowType.INSTANT,
            "TOGGLE_AUTO_MESSAGES": WorkflowType.INSTANT,
            "CREATE_AUTO_MESSAGE": WorkflowType.INSTANT,
            "UPDATE_AUTO_MESSAGE": WorkflowType.INSTANT,
            "DELETE_AUTO_MESSAGE": WorkflowType.INSTANT,
            "SEND_MANUAL_MESSAGE": WorkflowType.INSTANT,
            "GET_AUTO_MESSAGE_USAGE": WorkflowType.INSTANT,
            
            # FOLLOW Feature - UN SOLO WORKFLOW che gestisce tutto internamente
            "FOLLOW_USER": WorkflowType.INSTANT,
        }
        
        # Cleanup task
        self._cleanup_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()

    async def execute_workflow(
        self,
        user_id: str,
            workflow_key: str,
        params: Dict[str, Any],
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Main entry point for workflow execution.
        Handles both instant and persistent workflows.
        """
        if workflow_key not in WORKFLOW_MAP:
            raise ValueError(f"Workflow '{workflow_key}' not found")
        
        # Check permissions
        await self._check_permissions(user_id, workflow_key)
        
        # Check daily limits
        await self._check_daily_limits(user_id, workflow_key)
        
        # Get workflow function and type
        workflow_func = WORKFLOW_MAP[workflow_key]
        workflow_type = self.workflow_types.get(workflow_key, WorkflowType.INSTANT)
        
        # Generate workflow ID
        timestamp = int(datetime.now().timestamp() * 1000)
        workflow_id = f"{workflow_key}_{user_id}_{timestamp}"
        
        # TUTTI i workflow sono ora istantanei - la persistenza è gestita internamente
        # dai singoli workflow (es. workflow_follow_unified gestisce la sua automazione)
        
        # Create workflow state
        workflow_state = WorkflowState(
            workflow_id=workflow_id,
            user_id=user_id,
            workflow_key=workflow_key,
            workflow_type=workflow_type,
            status="queued",
            params=params.copy(),
            request_id=request_id
        )
        
        # Register workflow (tutti sono istantanei)
        self.workflows[workflow_id] = workflow_state
        
        logger.info(f"🚀 Starting workflow {workflow_key} for user {user_id} (ID: {workflow_id})")
        
        try:
            # Esegui workflow istantaneo e ritorna risultato
            result = await self._execute_instant_workflow(workflow_state, workflow_func)
            await self._log_usage(user_id, workflow_key)
            return {
                "success": True,
                "workflow_id": workflow_id,
                "result": result,
                "status": "completed"
            }
            
        except Exception as e:
            await self._handle_workflow_error(workflow_state, e)
            raise

    async def stop_workflow(self, user_id: str, workflow_key: str) -> bool:
        """
        DEPRECATO: Ora i workflow gestiscono internamente start/stop.
        Questo metodo è mantenuto per compatibilità ma non fa nulla.
        """
        logger.warning(f"⚠️ stop_workflow called for {workflow_key} - now managed internally by the workflow")
        return False

    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get the current status of a workflow."""
        workflow_state = self.workflows.get(workflow_id)
        if not workflow_state:
            return None
        
        return {
            "workflow_id": workflow_id,
            "workflow_key": workflow_state.workflow_key,
            "status": workflow_state.status,
            "progress": workflow_state.progress,
            "result": workflow_state.result,
            "error": workflow_state.error,
            "created_at": workflow_state.created_at.isoformat(),
            "started_at": workflow_state.started_at.isoformat() if workflow_state.started_at else None,
            "completed_at": workflow_state.completed_at.isoformat() if workflow_state.completed_at else None,
        }

    async def get_user_workflows(self, user_id: str) -> Dict[str, Any]:
        """Get all workflows for a user."""
        user_workflow_ids = self.user_workflows.get(user_id, {})
        user_workflows = {}
        
        for workflow_key, workflow_id in user_workflow_ids.items():
            status = await self.get_workflow_status(workflow_id)
            if status:
                user_workflows[workflow_key] = status
        
        return user_workflows

    async def _execute_instant_workflow(self, workflow_state: WorkflowState, workflow_func: Callable) -> Any:
        """Execute an instant workflow and return the result."""
        workflow_state.status = "running"
        workflow_state.started_at = datetime.now(timezone.utc)
        await self._update_workflow_state(workflow_state)
        
        try:
            # Add request_id to params for workflow functions that need it
            enhanced_params = workflow_state.params.copy()
            if workflow_state.request_id:
                enhanced_params["request_id"] = workflow_state.request_id
            
            # Execute the workflow function
            result = await workflow_func(workflow_state.user_id, enhanced_params)
            
            # Update state
            workflow_state.status = "completed"
            workflow_state.completed_at = datetime.now(timezone.utc)
            workflow_state.result = result
            
            # Notify completion with WORKFLOW_RESULT message
            await self._notify_workflow_completed(workflow_state)
            
            return result
            
        except Exception as e:
            workflow_state.status = "failed"
            workflow_state.error = str(e)
            workflow_state.completed_at = datetime.now(timezone.utc)
            
            # Notify failure with WORKFLOW_RESULT message
            await self._notify_workflow_failed(workflow_state)
            raise
        finally:
            # Cleanup instant workflows after completion
            asyncio.create_task(self._cleanup_workflow_after_delay(workflow_state.workflow_id, 300))

    # Metodi per workflow persistenti rimossi - ora gestiti internamente dai workflow

    async def _check_permissions(self, user_id: str, workflow_key: str):
        """Check if user has permissions for this workflow."""
        permissions = await get_user_permissions(user_id)
        if not permissions["is_plan_active"]:
            raise PermissionDeniedError(f"Plan '{permissions['plan_name']}' is not active.")
        if not permissions["features"].get(workflow_key, False):
            raise PermissionDeniedError(f"Feature '{workflow_key}' not available for your plan.")

    async def _check_daily_limits(self, user_id: str, workflow_key: str):
        """Check daily usage limits for this workflow."""
        permissions = await get_user_permissions(user_id)
        limit_key = f"max_{workflow_key}_per_day"
        limit = permissions["limits"].get(limit_key)
        
        if limit is not None and limit != -1:
            start_of_day = (
                datetime.now(timezone.utc)
                .replace(hour=0, minute=0, second=0, microsecond=0)
                .isoformat()
            )
            count_response = (
                supabase.table("usage_logs")
                .select("id", count="exact")
                .eq("user_id", user_id)
                .eq("feature_key", workflow_key)
                .gte("created_at", start_of_day)
                .execute()
            )
            if count_response.count >= limit:
                raise PermissionDeniedError(
                    f"You have reached the daily limit of {limit} uses for '{workflow_key}'."
                )

    async def _log_usage(self, user_id: str, workflow_key: str):
        """Log workflow usage for billing/limits tracking."""
        permissions = await get_user_permissions(user_id)
        limit_key = f"max_{workflow_key}_per_day"
        limit = permissions["limits"].get(limit_key)
        
        if limit is not None and limit != -1:
            supabase.table("usage_logs").insert(
                {"user_id": user_id, "feature_key": workflow_key}
            ).execute()

    # Metodi pubblici wrapper per i controlli
    async def check_permissions(self, user_id: str, workflow_key: str):
        """Public wrapper for permission checking."""
        await self._check_permissions(user_id, workflow_key)

    async def check_daily_limits(self, user_id: str, workflow_key: str):
        """Public wrapper for daily limits checking."""
        await self._check_daily_limits(user_id, workflow_key)

    async def log_usage(self, user_id: str, workflow_key: str):
        """Public wrapper for usage logging."""
        await self._log_usage(user_id, workflow_key)

    def _get_user_workflow(self, user_id: str, workflow_key: str) -> Optional[str]:
        """Get the workflow ID for a user's workflow of a specific type."""
        return self.user_workflows.get(user_id, {}).get(workflow_key)

    def _register_user_workflow(self, user_id: str, workflow_key: str, workflow_id: str):
        """Register a workflow for a user."""
        if user_id not in self.user_workflows:
            self.user_workflows[user_id] = {}
        self.user_workflows[user_id][workflow_key] = workflow_id

    def _unregister_user_workflow(self, user_id: str, workflow_key: str):
        """Unregister a workflow for a user."""
        if user_id in self.user_workflows:
            self.user_workflows[user_id].pop(workflow_key, None)
            if not self.user_workflows[user_id]:
                del self.user_workflows[user_id]

    async def _update_workflow_state(self, workflow_state: WorkflowState):
        """Update workflow state and notify connected clients."""
        workflow_state.updated_at = datetime.now(timezone.utc)
        await self._notify_workflow_update(workflow_state)

    async def _notify_workflow_update(self, workflow_state: WorkflowState):
        """Send workflow update notification to user."""
        notification = {
            "type": "WORKFLOW_UPDATE",
            "workflow_id": workflow_state.workflow_id,
            "workflow_key": workflow_state.workflow_key,
            "status": workflow_state.status,
            "progress": workflow_state.progress,
            "updated_at": workflow_state.updated_at.isoformat()
        }
        
        # Include request_id if available
        if workflow_state.request_id:
            notification["request_id"] = workflow_state.request_id
            
        await self._send_notification(workflow_state.user_id, notification)

    async def _notify_workflow_started(self, workflow_state: WorkflowState):
        """Notify that a workflow has started."""
        notification = {
            "type": "WORKFLOW_STARTED",
            "workflow_id": workflow_state.workflow_id,
            "workflow_key": workflow_state.workflow_key,
            "status": workflow_state.status,
            "started_at": workflow_state.started_at.isoformat() if workflow_state.started_at else None
        }
        await self._send_notification(workflow_state.user_id, notification)

    async def _notify_workflow_completed(self, workflow_state: WorkflowState):
        """Notify that a workflow has completed."""
        notification = {
            "type": "WORKFLOW_RESULT",
            "workflow_id": workflow_state.workflow_id,
            "workflow_key": workflow_state.workflow_key,
            "status": "success",
            "data": workflow_state.result,
            "completed_at": workflow_state.completed_at.isoformat() if workflow_state.completed_at else None
        }
        
        # Include request_id if available
        if workflow_state.request_id:
            notification["request_id"] = workflow_state.request_id
            
        await self._send_notification(workflow_state.user_id, notification)

    async def _notify_workflow_failed(self, workflow_state: WorkflowState):
        """Notify that a workflow has failed."""
        notification = {
            "type": "WORKFLOW_RESULT",
            "workflow_id": workflow_state.workflow_id,
            "workflow_key": workflow_state.workflow_key,
            "status": "error",
            "error": workflow_state.error,
            "completed_at": workflow_state.completed_at.isoformat() if workflow_state.completed_at else None
        }
        
        # Include request_id if available
        if workflow_state.request_id:
            notification["request_id"] = workflow_state.request_id
            
        await self._send_notification(workflow_state.user_id, notification)

    async def _notify_workflow_cancelled(self, workflow_state: WorkflowState):
        """Notify that a workflow has been cancelled."""
        notification = {
            "type": "WORKFLOW_CANCELLED",
            "workflow_id": workflow_state.workflow_id,
            "workflow_key": workflow_state.workflow_key,
            "status": workflow_state.status,
            "completed_at": workflow_state.completed_at.isoformat() if workflow_state.completed_at else None
        }
        await self._send_notification(workflow_state.user_id, notification)

    async def _send_notification(self, user_id: str, notification: Dict[str, Any]):
        """Send notification to user via WebSocket."""
        try:
            await manager.send_to_user_react(user_id, notification)
        except Exception as e:
            logger.warning(f"⚠️ Could not send notification to user {user_id}: {e}")

    async def _handle_workflow_error(self, workflow_state: WorkflowState, error: Exception):
        """Handle workflow execution error."""
        workflow_state.status = "failed"
        workflow_state.error = str(error)
        workflow_state.completed_at = datetime.now(timezone.utc)
        await self._update_workflow_state(workflow_state)
        await self._notify_workflow_failed(workflow_state)

    def _start_cleanup_task(self):
        """Start the periodic cleanup task."""
        if not self._cleanup_task or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())

    async def _periodic_cleanup(self):
        """Periodically clean up completed workflows."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._cleanup_completed_workflows()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")

    async def _cleanup_completed_workflows(self):
        """Remove old completed workflows to prevent memory leaks."""
        cutoff_time = datetime.now(timezone.utc).timestamp() - 3600  # 1 hour ago
        to_remove = []
        
        for workflow_id, workflow_state in self.workflows.items():
            if (workflow_state.status in ["completed", "failed", "cancelled"] and
                workflow_state.completed_at and
                workflow_state.completed_at.timestamp() < cutoff_time):
                to_remove.append(workflow_id)
        
        for workflow_id in to_remove:
            workflow_state = self.workflows.pop(workflow_id, None)
            if workflow_state:
                self._unregister_user_workflow(workflow_state.user_id, workflow_state.workflow_key)
                logger.debug(f"🗑️ Cleaned up old workflow {workflow_id}")

    async def _cleanup_workflow_after_delay(self, workflow_id: str, delay_seconds: int):
        """Clean up a specific workflow after a delay."""
        await asyncio.sleep(delay_seconds)
        workflow_state = self.workflows.pop(workflow_id, None)
        if workflow_state:
            self._unregister_user_workflow(workflow_state.user_id, workflow_state.workflow_key)
            logger.debug(f"🗑️ Cleaned up workflow {workflow_id} after delay")

    async def start_background_job(self, user_id: str, job_type: BackgroundJobType, config: Dict[str, Any] = None, request_id: Optional[str] = None):
        """Start a background job for a user."""
        job_key = f"{user_id}_{job_type.value}"
        
        # Stop existing job if running
        if job_key in self.running_background_jobs:
            await self.stop_background_job(user_id, job_type, request_id)
        
        # Start new job (for now, just track it - implement actual logic later)
        # TODO: Implement actual background job execution
        
        # Track the job
        self.running_background_jobs[job_key] = {
            "user_id": user_id,
            "job_type": job_type,
            "started_at": datetime.now(timezone.utc),
            "config": config or {},
            "request_id": request_id
        }
        
        self.background_job_states[job_key] = {
            "status": "running",
            "started_at": datetime.now(timezone.utc),
            "last_activity": datetime.now(timezone.utc),
            "request_id": request_id
        }
        
        logger.info(f"🚀 Started background job {job_type.value} for user {user_id}")
        
        # Notify user
        notification = {
            "type": "background_job_started",
            "job_type": job_type.value,
            "message": f"Background job {job_type.value} started successfully"
        }
        if request_id:
            notification["request_id"] = request_id
            
        await self.connection_manager.send_to_user_react(user_id, notification)

    async def stop_background_job(self, user_id: str, job_type: BackgroundJobType, request_id: Optional[str] = None):
        """Stop a background job for a user."""
        job_key = f"{user_id}_{job_type.value}"
        
        if job_key not in self.running_background_jobs:
            return False
        
        # Stop the actual job (for now, just remove tracking - implement actual logic later)
        # TODO: Implement actual background job stopping
        
        # Remove from tracking
        self.running_background_jobs.pop(job_key, None)
        
        # Update state
        if job_key in self.background_job_states:
            self.background_job_states[job_key]["status"] = "stopped"
            self.background_job_states[job_key]["stopped_at"] = datetime.now(timezone.utc)
        
        logger.info(f"⏹️ Stopped background job {job_type.value} for user {user_id}")
        
        # Notify user
        notification = {
            "type": "background_job_stopped",
            "job_type": job_type.value,
            "message": f"Background job {job_type.value} stopped"
        }
        if request_id:
            notification["request_id"] = request_id
            
        await self.connection_manager.send_to_user_react(user_id, notification)
        
        return True

    def get_background_job_status(self, user_id: str, job_type: BackgroundJobType = None) -> Dict[str, Any]:
        """Get status of background jobs for a user."""
        if job_type:
            job_key = f"{user_id}_{job_type.value}"
            return self.background_job_states.get(job_key, {"status": "not_running"})
        
        # Return all jobs for user
        user_jobs = {}
        for job_key, state in self.background_job_states.items():
            if job_key.startswith(f"{user_id}_"):
                job_type_str = job_key.replace(f"{user_id}_", "")
                user_jobs[job_type_str] = state
        
        return user_jobs

    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the workflow manager."""
        total_workflows = len(self.workflows)
        running_workflows = len([w for w in self.workflows.values() if w.status == "running"])
        completed_workflows = len([w for w in self.workflows.values() if w.status == "completed"])
        failed_workflows = len([w for w in self.workflows.values() if w.status == "failed"])
        
        # Background jobs stats
        total_background_jobs = len(self.running_background_jobs)
        running_background_jobs = len([j for j in self.background_job_states.values() if j.get("status") == "running"])
        
        return {
            "total_workflows": total_workflows,
            "running_workflows": running_workflows,
            "completed_workflows": completed_workflows,
            "failed_workflows": failed_workflows,
            "active_users": len(self.user_workflows),
            "total_background_jobs": total_background_jobs,
            "running_background_jobs": running_background_jobs
        }


# Global instance
workflow_manager = WorkflowManager()
