# app/services/background_jobs.py
import asyncio
import logging
import time
from typing import Dict

from app.services.connection_manager import manager

logger = logging.getLogger(__name__)

class BackgroundJobs:
    def __init__(self):
        self.running = False
        self.login_check_task = None
    
    async def start(self):
        """Start background jobs"""
        self.running = True
        logger.info("Starting background jobs")
        self.login_check_task = asyncio.create_task(self.periodic_login_check())
    
    async def stop(self):
        """Stop background jobs"""
        self.running = False
        logger.info("Stopping background jobs")
        
        if self.login_check_task and not self.login_check_task.done():
            self.login_check_task.cancel()
            try:
                await self.login_check_task
            except asyncio.CancelledError:
                pass
    
    async def periodic_login_check(self):
        """Check Vinted login status periodically for connected users"""
        while self.running:
            try:
                # Get all users with extension connected
                connected_users = [
                    user_id for user_id in manager.extension_connections 
                    if manager.is_extension_online(user_id)
                ]
                
                if connected_users:
                    logger.debug(f"Background login check for {len(connected_users)} users")
                
                for user_id in connected_users:
                    # Check cache age
                    ext_data = manager.extension_connections.get(user_id, {})
                    last_check = ext_data.get("last_login_check", 0)
                    cache_age = time.time() - last_check if last_check else None
                    
                    # If cache is older than 4 minutes, refresh it
                    # Using 4 minutes instead of 5 to be proactive
                    if cache_age is None or cache_age > 240:  # 4 minutes
                        logger.debug(f"Background refresh of login for user {user_id}")
                        try:
                            # Import here to avoid circular imports
                            from app.services.workflow_manager import workflow_manager
                            
                            # Execute without awaiting to not block the loop
                            asyncio.create_task(workflow_manager.execute_workflow(
                                user_id=user_id,
                                workflow_key="CHECK_VINTED_LOGIN_STATUS",
                                params={}
                            ))
                        except Exception as e:
                            logger.error(f"Error refreshing login for {user_id}: {e}")
                
                # Check every 30 seconds
                await asyncio.sleep(30)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic login check: {e}")
                await asyncio.sleep(30)

# Global instance
background_jobs = BackgroundJobs()
