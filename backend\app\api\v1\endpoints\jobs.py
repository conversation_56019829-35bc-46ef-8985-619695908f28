# app/api/v1/endpoints/jobs.py
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, List
import logging

from app.core.security import get_user_id_from_token
from app.services.job_controller import job_controller

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/permissions/{job_type}")
async def check_job_permissions(
    job_type: str,
    user_id: str = Depends(get_user_id_from_token)
) -> Dict[str, Any]:
    """
    Verifica se l'utente ha i permessi per avviare un job specifico
    e restituisce informazioni sui limiti
    """
    try:
        permission_check = await job_controller.check_job_permissions(user_id, job_type)
        return {
            "job_type": job_type,
            "user_id": user_id,
            **permission_check
        }
    except Exception as e:
        logger.exception(f"Error checking job permissions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error checking permissions: {str(e)}"
        )


@router.get("/status")
async def get_user_jobs(
    user_id: str = Depends(get_user_id_from_token)
) -> List[Dict[str, Any]]:
    """
    Ottiene tutti i job attivi dell'utente
    """
    try:
        jobs = await job_controller.get_user_jobs(user_id)
        return jobs
    except Exception as e:
        logger.exception(f"Error getting user jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting jobs: {str(e)}"
        )


@router.get("/status/{job_id}")
async def get_job_status(
    job_id: str,
    user_id: str = Depends(get_user_id_from_token)
) -> Dict[str, Any]:
    """
    Ottiene lo status di un job specifico
    """
    try:
        status_info = await job_controller.get_job_status(job_id, user_id)
        return status_info
    except Exception as e:
        logger.exception(f"Error getting job status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job status: {str(e)}"
        )


@router.post("/stop/{job_id}")
async def stop_job(
    job_id: str,
    user_id: str = Depends(get_user_id_from_token)
) -> Dict[str, Any]:
    """
    Ferma un job in esecuzione
    """
    try:
        # Verifica che il job appartenga all'utente
        if not job_controller.is_job_owned_by_user(job_id, user_id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found or not owned by user"
            )
        
        success = await job_controller.stop_job(job_id)
        if success:
            return {"job_id": job_id, "status": "stopped", "success": True}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to stop job"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error stopping job: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error stopping job: {str(e)}"
        )


@router.post("/pause/{job_id}")
async def pause_job(
    job_id: str,
    user_id: str = Depends(get_user_id_from_token)
) -> Dict[str, Any]:
    """
    Mette in pausa un job
    """
    try:
        # Verifica che il job appartenga all'utente
        if not job_controller.is_job_owned_by_user(job_id, user_id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found or not owned by user"
            )
        
        success = await job_controller.pause_job(job_id, "User request")
        if success:
            return {"job_id": job_id, "status": "paused", "success": True}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to pause job"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error pausing job: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error pausing job: {str(e)}"
        )


@router.post("/resume/{job_id}")
async def resume_job(
    job_id: str,
    user_id: str = Depends(get_user_id_from_token)
) -> Dict[str, Any]:
    """
    Riprende un job in pausa
    """
    try:
        # Verifica che il job appartenga all'utente
        if not job_controller.is_job_owned_by_user(job_id, user_id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found or not owned by user"
            )
        
        success = await job_controller.resume_job(job_id)
        if success:
            return {"job_id": job_id, "status": "running", "success": True}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to resume job"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error resuming job: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error resuming job: {str(e)}"
        )


@router.get("/logs/{job_id}")
async def get_job_logs(
    job_id: str,
    limit: int = 50,
    user_id: str = Depends(get_user_id_from_token)
) -> List[Dict[str, Any]]:
    """
    Ottiene i log di un job
    """
    try:
        logs = await job_controller.get_job_logs(job_id, user_id, limit)
        return logs
    except Exception as e:
        logger.exception(f"Error getting job logs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job logs: {str(e)}"
        )


@router.get("/stats")
async def get_job_stats(
    user_id: str = Depends(get_user_id_from_token)
) -> Dict[str, Any]:
    """
    Ottiene le statistiche sui job (solo per admin/debug)
    """
    try:
        # Potremmo aggiungere controlli admin qui
        stats = job_controller.get_stats()
        return stats
    except Exception as e:
        logger.exception(f"Error getting job stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job stats: {str(e)}"
        )
