# app/services/vinted_workflows.py
import asyncio
from collections import defaultdict
from datetime import date, datetime, timezone
import json
import re
import base64
import logging
import time
from typing import Any, Dict, Coroutine, Optional

import aiohttp
from app.models.plans import get_user_permissions, PermissionDeniedError
from app.services.vinted_fetcher import perform_fetch_via_extension, get_active_domain
from app.services.connection_manager import manager
from app.utils import image_utils
from app.db.supabase_client import supabase
from app.utils.time import format_time_ago
from app.utils.token import _get_vinted_csrf_token
# Import likes workflows
from app.services.likes_workflows import (
    workflow_sync_vinted_likes,
    workflow_get_recent_likes_with_sync
)

logger = logging.getLogger(__name__)





def vinted_login_status_processor(raw_data: str, http_status_code: int) -> dict:
    if http_status_code != 200:
        return {"is_logged_in": False, "reason": f"HTTP {http_status_code}"}

    try:
        # Validate input data
        if not raw_data or raw_data.strip() == "":
            logger.warning("⚠️ Empty or None raw_data received in vinted_login_status_processor")
            return {"is_logged_in": False, "reason": "Empty response received"}
        
        # Try to parse JSON
        try:
            data = json.loads(raw_data)
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON decode error in vinted_login_status_processor: {e}")
            logger.debug(f"🔍 Raw data received: {raw_data[:200]}...")  # Log first 200 chars
            return {"is_logged_in": False, "reason": "Invalid JSON response"}
        
        # Validate data structure
        if not isinstance(data, dict):
            logger.error(f"❌ Expected dict but got {type(data)} in vinted_login_status_processor")
            return {"is_logged_in": False, "reason": "Invalid response structure"}
        
        return {"is_logged_in": bool(data.get("access_token"))}
        
    except Exception as e:
        logger.error(f"❌ Unexpected error in vinted_login_status_processor: {e}")
        logger.exception("🔍 Full traceback:")
        return {"is_logged_in": False, "reason": "Failed to parse response"}


def vinted_user_details_processor(raw_data: str, http_status_code: int) -> dict:
    if http_status_code != 200:
        return {"error": f"HTTP {http_status_code}"}
    
    try:
        # Validate input data
        if not raw_data or raw_data.strip() == "":
            logger.warning("⚠️ Empty or None raw_data received in vinted_user_details_processor")
            return {"error": "Empty response received"}
        
        # Try to parse JSON
        try:
            data = json.loads(raw_data)
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON decode error in vinted_user_details_processor: {e}")
            logger.debug(f"🔍 Raw data received: {raw_data[:200]}...")  # Log first 200 chars
            return {"error": "Invalid JSON response"}
        
        # Validate data structure
        if not isinstance(data, dict):
            logger.error(f"❌ Expected dict but got {type(data)} in vinted_user_details_processor")
            return {"error": "Invalid response structure"}
        
        user_info = data.get("user", {})
        if not isinstance(user_info, dict):
            logger.warning(f"⚠️ Invalid user_info structure: {type(user_info)}")
            user_info = {}
            
        v_id, v_user = user_info.get("id"), user_info.get("login")
        if v_id and v_user:
            return {"vinted_user_id": v_id, "vinted_username": v_user}
        return {"error": "User ID/username missing"}
        
    except Exception as e:
        logger.error(f"❌ Unexpected error in vinted_user_details_processor: {e}")
        logger.exception("🔍 Full traceback:")
        return {"error": "Failed to parse user details"}


# --- Workflow Esistenti (Preservati dalla tua versione) ---
async def workflow_get_user_products(user_id: str, params: dict) -> dict:
    """
    Fetches all products from the user's Vinted wardrobe using the wardrobe endpoint.
    Supports progressive loading - sends each page to frontend as it's fetched.
    """
    logger.info(f"👕 Starting workflow_get_user_products for user {user_id}")
    
    domain = get_active_domain(user_id, params)
    logger.info(f"🌐 Using domain: {domain}")

    vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")

    # Check if vinted_user_info is available
    if not (vinted_user_info and vinted_user_info.get("id")):
        # Try to get the login status first
        try:
            login_result = await workflow_check_vinted_login(user_id, params)

            if not login_result.get("is_logged_in"):
                raise ValueError(
                    "User not logged in to Vinted. Please log in before retrieving products."
                )

            # After successful login check, try to get vinted_user_info again
            vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")

            if not (vinted_user_info and vinted_user_info.get("id")):
                raise ValueError(
                    "Vinted user ID not available even after login check."
                )

        except Exception as e:
            raise ValueError(f"Unable to verify login status: {str(e)}")

    vinted_id = vinted_user_info["id"]
    all_items = []
    page = 1
    MAX_PAGES = 100  # Reasonable limit to avoid infinite loops
    total_pages = 1
    global_index = 0  # Global index to maintain order across pages

    # Get products with progressive loading
    logger.info("👕 Fetching products from wardrobe API with progressive loading...")

    while page <= MAX_PAGES and page <= total_pages:
        # URL without order parameter to maintain Vinted's natural order
        url = f"https://{domain}/api/v2/wardrobe/{vinted_id}/items?page={page}&per_page=20"
        logger.info(f"📋 Fetching products page {page}: {url}")

        try:
            resp = await perform_fetch_via_extension(
                user_id,
                {
                    "url": url,
                    "method": "GET",
                    "headers": {"accept": "application/json"},
                },
            )

            logger.info(f"📊 Products page {page} response status: {resp.get('http_status_code', 'UNKNOWN')}")

            if resp.get("http_status_code", 500) != 200:
                logger.warning(f"❌ Failed to fetch products page {page}: HTTP {resp.get('http_status_code')}")
                break

            data = json.loads(resp["raw_response_body"])
            items_on_page = data.get("items", [])
            logger.info(f"👕 Found {len(items_on_page)} products on page {page}")

            if not items_on_page:
                logger.info(f"📝 No more products found on page {page}, stopping")
                break

            # Process products from this page
            page_products = []
            for item in items_on_page:
                try:
                    # Transform the product data to match frontend expectations
                    photo_data = item.get("photo", {})
                    photos_array = item.get("photos", [])
                    
                    # Get the main photo URL - prioritize the first photo from photos array
                    photo_url = None
                    if photos_array and len(photos_array) > 0:
                        # Get the first photo (main photo) from the photos array
                        main_photo = photos_array[0]
                        photo_url = main_photo.get("url")
                        logger.debug(f"📸 Found main photo URL for product {item.get('id')}: {photo_url}")
                    elif photo_data:
                        # Fallback to photo object if photos array is empty
                        photo_url = (
                            photo_data.get("url") or 
                            photo_data.get("thumb_url") or 
                            photo_data.get("full_size_url") or
                            photo_data.get("medium_url")
                        )
                        logger.debug(f"📸 Using fallback photo URL for product {item.get('id')}: {photo_url}")
                    
                    if not photo_url:
                        logger.warning(f"⚠️ No photo URL found for product {item.get('id')}")
                    
                    processed_product = {
                        "id": item.get("id"),
                        "title": item.get("title", "Unknown Product"),
                        "price": item.get("price", {}),
                        "currency": item.get("currency"),
                        "brand": item.get("brand"),
                        "size": item.get("size"),
                        "status": item.get("status"),
                        "photo": photo_data,
                        "photos": photos_array,  # Include full photos array
                        "photo_url": photo_url,  # Add processed photo URL
                        "url": item.get("url"),
                        "domain": domain,  # Add domain for frontend URL construction
                        "_order_index": global_index,  # Global order index
                        # Keep original data for reference
                        "original_data": item
                    }
                    page_products.append(processed_product)
                    all_items.append(processed_product)
                    global_index += 1  # Increment global index
                    
                except Exception as e:
                    logger.warning(f"⚠️ Error processing product {item.get('id', 'unknown')}: {e}")
                    continue

            # Get pagination info
            pagination_info = data.get("pagination", {})
            if page == 1:
                total_pages = pagination_info.get("total_pages", 1)
            
            current_page = pagination_info.get("current_page", page)
            logger.info(f"📄 Pagination: {current_page}/{total_pages}")

            # 🚀 SEND PROGRESSIVE UPDATE TO FRONTEND
            if page_products:  # Only send if we have products
                try:
                    await manager.send_to_user_react(
                        user_id,
                        {
                            "type": "VINTED_PRODUCTS_PAGE",
                            "workflow_key": "GET_USER_PRODUCTS",
                            "page_number": page,
                            "products": page_products,
                            "pagination": {
                                "current_page": current_page,
                                "total_pages": total_pages,
                                "is_last_page": current_page >= total_pages,
                                "total_loaded": len(all_items)
                            }
                        }
                    )
                    logger.info(f"📤 Sent page {page} with {len(page_products)} products to frontend")
                except Exception as e:
                    logger.error(f"❌ Failed to send page {page} to frontend: {e}")

            # Check if we've reached the last page
            if current_page >= total_pages:
                logger.info("✅ Reached last page")
                break

            page += 1
            await asyncio.sleep(0.5)  # Rate limiting

        except Exception as e:
            logger.error(f"❌ Error fetching products page {page} for user {user_id}: {e}")
            break

    logger.info(f"📊 Total products fetched: {len(all_items)} across {page - 1} pages")

    # Final response with all products
    return {
        "products": all_items,
        "total_count": len(all_items),
        "pages_fetched": page - 1,
        "loading_complete": True
    }



async def workflow_check_vinted_login(user_id: str, params: dict) -> dict:
    domain = get_active_domain(user_id, params)
    final_result = {}
    
    try:
        force_refresh = params.get("force_refresh", False)
        
        # Check cache age
        ext_data = manager.extension_connections.get(user_id, {})
        vinted_user_info = ext_data.get("vinted_user_info")
        last_check = ext_data.get("last_login_check", 0)
        cache_age = time.time() - last_check if last_check else None
        
        # If cache is fresh and not forcing refresh, return cached data
        if not force_refresh and cache_age is not None and cache_age < 300 and vinted_user_info:
            logger.debug(f"Using cached Vinted login info for user {user_id}, age: {cache_age:.1f}s")
            return {
                "is_logged_in": bool(vinted_user_info and vinted_user_info.get("id")),
                "vinted_user_id": vinted_user_info.get("id") if vinted_user_info else None,
                "vinted_username": vinted_user_info.get("username") if vinted_user_info else None,
                "from_cache": True,
                "cache_age": int(cache_age)
            }
        
        # Cache not fresh or forced refresh, check login status
        logger.debug(f"Checking Vinted login for user {user_id}")
        
        refresh_resp = await perform_fetch_via_extension(user_id, {"url": f"https://{domain}/web/api/auth/refresh", "method": "POST"})
        session_status = vinted_login_status_processor(refresh_resp["raw_response_body"], refresh_resp.get("http_status_code"))
        final_result.update(session_status)
        if not session_status.get("is_logged_in"):
            await manager.notify_vinted_login_update(user_id, False, None, None)
            logger.info(f"🔍 [CHECK_VINTED_LOGIN] User {user_id} not logged in, returning: {final_result}")
            return final_result
        details_resp = await perform_fetch_via_extension(user_id, {"url": f"https://{domain}/api/v2/users/current", "method": "GET", "headers": {"Accept": "application/json"}})
        user_details = vinted_user_details_processor(details_resp["raw_response_body"], details_resp.get("http_status_code"))
        
        # Check if user_details contains an error
        if "error" in user_details:
            logger.warning(f"🔍 [CHECK_VINTED_LOGIN] User details error for {user_id}: {user_details}")
            # If we can't get user details, still return login status but with default values
            final_result.update({
                "vinted_user_id": None,
                "vinted_username": None,
                "reason": user_details.get("error", "Unknown error getting user details")
            })
            await manager.notify_vinted_login_update(user_id, False, None, None)
        else:
            # Success case - user details were retrieved
            final_result.update(user_details)
            vinted_id, vinted_username = final_result.get("vinted_user_id"), final_result.get("vinted_username")
            if vinted_id and vinted_username:
                manager.update_extension_data(user_id, {"vinted_user_info": {"id": vinted_id, "username": vinted_username}})
                await manager.notify_vinted_login_update(user_id, True, vinted_id, vinted_username)
            else: 
                await manager.notify_vinted_login_update(user_id, False, None, None)
        
        # Add from_cache flag for API consistency
        final_result["from_cache"] = False
        
        # DEBUG: Log the final result being returned
        logger.info(f"🔍 [CHECK_VINTED_LOGIN] User {user_id} final result: {final_result}")
        
    except (ConnectionError, TimeoutError) as e: 
        final_result = {"is_logged_in": None, "reason": str(e)}
        logger.error(f"🔍 [CHECK_VINTED_LOGIN] Connection error for {user_id}: {final_result}")
    except Exception as e: 
        final_result = {"is_logged_in": False, "reason": f"Internal error: {str(e)}"}
        logger.error(f"🔍 [CHECK_VINTED_LOGIN] Exception for {user_id}: {final_result}")
    return final_result

async def workflow_vinted_stats(user_id: str, params: dict) -> dict:
    """
    Fetches real user statistics from Vinted API using the current user endpoint.
    """
    try:
        # 1. Check if user is logged in to Vinted - simplified approach
        vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
        if not (vinted_user_info and vinted_user_info.get("id")):
            # Simple login check without calling another workflow
            try:
                domain = get_active_domain(user_id, params)
                refresh_resp = await perform_fetch_via_extension(
                    user_id, 
                    {"url": f"https://{domain}/web/api/auth/refresh", "method": "POST"}
                )
                session_status = vinted_login_status_processor(
                    refresh_resp["raw_response_body"], 
                    refresh_resp.get("http_status_code")
                )
                
                if not session_status.get("is_logged_in"):
                    return {"error": "User not logged into Vinted. Please log in to retrieve statistics."}
                    
            except Exception as e:
                return {"error": f"Unable to verify login status: {str(e)}"}

        # 2. Get user data from Vinted API
        domain = get_active_domain(user_id, params)
        api_url = f"https://{domain}/api/v2/users/current"
        logger.info(f"Fetching Vinted stats from: {api_url}")
        
        try:
            details_resp = await perform_fetch_via_extension(
                user_id, 
                {
                    "url": api_url,
                    "method": "GET",
                    "headers": {"Accept": "application/json"}
                }
            )

            if details_resp.get("http_status_code", 500) != 200:
                logger.warning(f"Vinted API returned HTTP {details_resp.get('http_status_code')}")
                return {
                    "feedbacks": "N/A",
                    "balance": "N/A",
                    "items_for_sale": "N/A"
                }

            # 3. Parse the JSON response
            user_data = json.loads(details_resp.get("raw_response_body", "{}"))
            logger.debug(f"📊 Received user data for stats (user_id: {user_id})")
            
            # The main 'user' key contains all necessary information
            user_info = user_data.get("user")
            if not user_info:
                logger.warning("Vinted API response does not contain user information")
                return {
                    "feedbacks": "N/A",
                    "balance": "N/A", 
                    "items_for_sale": "N/A"
                }
            
            # Calculate total feedbacks from the correct fields
            total_feedback = (
                user_info.get("positive_feedback_count", 0) + 
                user_info.get("neutral_feedback_count", 0) + 
                user_info.get("negative_feedback_count", 0)
            )
            
            # Get number of items for sale from the correct field
            items_for_sale = user_info.get("item_count", 0)
            
            # Try to get balance information (defensive, as it may not be in the response)
            balance_info = user_data.get("balance", {})
            balance_amount = balance_info.get("amount", "0.00")
            # Use currency from user profile as reliable fallback
            balance_currency = balance_info.get("currency") or user_info.get("currency", "EUR")

            # 4. Format the final response
            stats_response = {
                "feedbacks": total_feedback,
                "items_for_sale": items_for_sale,
                "balance": f"{balance_amount} {balance_currency}",
                "followers_count": user_info.get("followers_count", 0),
                "following_count": user_info.get("following_count", 0)
            }
            
            logger.info(f"Successfully retrieved Vinted stats for user {user_id}: {stats_response}")
            return stats_response
            
        except json.JSONDecodeError:
            logger.error("Invalid or malformed JSON response from Vinted API")
            return {
                "feedbacks": "N/A",
                "balance": "N/A",
                "items_for_sale": "N/A"
            }
        except Exception as e:
            logger.error(f"Error retrieving statistics for user {user_id}: {e}")
            return {
                "feedbacks": "N/A",
                "balance": "N/A",
                "items_for_sale": "N/A"
            }
            
    except Exception as e:
        logger.error(f"General error in stats workflow for user {user_id}: {e}")
        return {
            "feedbacks": "N/A",
            "balance": "N/A",
            "items_for_sale": "N/A"
        }


async def workflow_repost_item(user_id: str, params: dict) -> dict:
    """
    Repost implementation with real-time feedback.
    Always uses the interactive version that sends progress updates.
    """
    item_id = params.get("item_id")
    if not item_id: 
        raise ValueError("Item ID missing for repost.")
    
    # Always use interactive repost with real-time feedback
    return await _legacy_repost_item(user_id, params)


async def _legacy_repost_item(user_id: str, params: dict) -> dict:
    """
    Complete legacy synchronous repost implementation.
    Updated to work with the new workflow system.
    """
    item_id = params.get("item_id")
    if not item_id: 
        raise ValueError("Item ID missing for repost.")
    
    domain = get_active_domain(user_id, params)
    request_id = params.get("request_id")

    async def send_feedback(message: str):
        if request_id:  # Only send feedback if request_id is provided
            await manager.send_to_user_react(
                user_id, 
                {
                    "type": "WORKFLOW_FEEDBACK", 
                    "workflow_key": "REPOST_ITEM", 
                    "request_id": request_id, 
                    "message": message
                }
            )

    try:
        await send_feedback("🚀 Repost started! Processing...")
        await send_feedback("Phase 1/7: Getting security token...")
        csrf_resp = await perform_fetch_via_extension(
            user_id, 
            {"url": f"https://{domain}/items/new", "method": "GET"}
        )
        csrf_token_match = re.search(
            r'CSRF_TOKEN[^0-9A-Za-z]*([0-9a-fA-F]{8}(?:-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12})', 
            csrf_resp.get("raw_response_body", "")
        )
        if not csrf_token_match: 
            raise RuntimeError("Unable to get CSRF token.")
        csrf_token = csrf_token_match.group(1)

        await send_feedback("Phase 2/7: Getting product details...")
        item_details_resp = await perform_fetch_via_extension(
            user_id, 
            {
                "url": f"https://{domain}/api/v2/item_upload/items/{item_id}", 
                "method": "GET", 
                "headers": {"x-csrf-token": csrf_token}
            }
        )
        item_data = json.loads(item_details_resp.get("raw_response_body", "{}")).get("item", {})
        if not item_data: 
            raise RuntimeError("Unable to retrieve item details.")

        photo_urls = [p.get("full_size_url") for p in item_data.get("photos", []) if p.get("full_size_url")]
        await send_feedback(f"Phase 3/7: Downloading {len(photo_urls)} images...")
        
        async with aiohttp.ClientSession() as session:
            image_tasks = [session.get(url) for url in photo_urls]
            image_responses = await asyncio.gather(*image_tasks)
            image_bytes_list = [await resp.read() for resp in image_responses]

        await send_feedback("Phase 4/7: Modifying images...")
        loop = asyncio.get_running_loop()
        processing_tasks = [
            loop.run_in_executor(None, image_utils.alter_photo, img_bytes) 
            for img_bytes in image_bytes_list
        ]
        altered_image_bytes_list = await asyncio.gather(*processing_tasks)

        await send_feedback(f"Phase 5/7: Uploading {len(altered_image_bytes_list)} new images...")
        upload_tasks = []
        for img_bytes in altered_image_bytes_list:
            fetch_details_for_upload = {
                "job_type": "UPLOAD_PROCESSED_IMAGE",
                "image_base64": base64.b64encode(img_bytes).decode('utf-8'),
                "domain": domain,
                "csrf_token": csrf_token
            }
            upload_tasks.append(perform_fetch_via_extension(user_id, fetch_details_for_upload))
        
        upload_results = await asyncio.gather(*upload_tasks)
        new_photos_data = [res.get("data") for res in upload_results if res.get("status") == "success"]
        if len(new_photos_data) != len(photo_urls): 
            raise RuntimeError(f"Upload failed for one or more images.")

        await send_feedback("Phase 6/7: Creating new listing...")
        draft_payload = {
            "item": {
                k: v for k, v in {
                    "title": item_data.get("title"),
                    "description": item_data.get("description"),
                    "brand_id": item_data.get("brand_dto", {}).get("id"),
                    "size_id": item_data.get("size_id"),
                    "catalog_id": item_data.get("catalog_id"),
                    "status_id": item_data.get("status_id"),
                    "price": item_data.get("price", {}).get("amount"),
                    "currency": item_data.get("currency"),
                    "package_size_id": item_data.get("package_size_id"),
                    "color_ids": [item_data.get("color1_id"), item_data.get("color2_id")],
                    "assigned_photos": [
                        {"id": p.get("id"), "orientation": p.get("orientation", 0)} 
                        for p in new_photos_data
                    ]
                }.items() if v is not None
            }
        }
        
        draft_resp = await perform_fetch_via_extension(
            user_id, 
            {
                "url": f"https://{domain}/api/v2/item_upload/items", 
                "method": "POST", 
                "headers": {
                    "content-type": "application/json", 
                    "x-csrf-token": csrf_token
                }, 
                "body": json.dumps(draft_payload)
            }
        )
        if draft_resp.get("http_status_code", 500) >= 400: 
            raise RuntimeError(f"Error creating draft: {draft_resp.get('raw_response_body')}")

        await send_feedback("Phase 7/7: Archiving old listing...")
        await perform_fetch_via_extension(
            user_id, 
            {
                "url": f"https://{domain}/api/v2/items/{item_id}/is_hidden", 
                "method": "PUT", 
                "headers": {
                    "content-type": "application/json", 
                    "x-csrf-token": csrf_token
                }, 
                "body": json.dumps({"is_hidden": True})
            }
        )
        
        await send_feedback("✅ Repost completed successfully!")
        return {"status": "success", "message": f"Item {item_id} reposted successfully!"}
        
    except Exception as e:
        logger.error(f"Error during reposting item {item_id}: {e}")
        await send_feedback(f"Error: {str(e)}")
        raise


# ## NUOVO ## Workflow per l'eliminazione di un articolo


async def workflow_delete_item(user_id: str, params: dict) -> dict:
    """

    Gestisce l'eliminazione di un articolo specifico su Vinted.

    """

    item_id = params.get("item_id")

    if not item_id:
        raise ValueError(
            "The item ID (item_id) is missing and required for deletion."
        )

    domain = get_active_domain(user_id, params)

    request_id = params.get("request_id")  # Opzionale, per feedback se necessario

    # Messaggio per il frontend, simile al repost

    async def send_feedback(message: str):
        if request_id:  # Invia feedback solo se il frontend ha fornito un request_id
            await manager.send_to_user_react(
                user_id,
                {
                    "type": "WORKFLOW_FEEDBACK",
                    "workflow_key": "DELETE_ITEM",
                    "request_id": request_id,
                    "message": message,
                },
            )

    # Fase 1: Ottenimento del token di sicurezza (CSRF)

    # È fondamentale per qualsiasi azione che modifica i dati (creare, aggiornare, eliminare).

    await send_feedback("🗑️ Deletion started! Processing...")
    await send_feedback("Phase 1/2: Getting security token...")

    try:
        # We use /items/new because it's a reliable page to find a fresh token.

        csrf_resp = await perform_fetch_via_extension(
            user_id, {"url": f"https://{domain}/items/new", "method": "GET"}
        )

        # Extract the CSRF token from the page HTML.

        csrf_token_match = re.search(
            r"CSRF_TOKEN[^0-9A-Za-z]*([0-9a-fA-F]{8}(?:-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12})",
            csrf_resp.get("raw_response_body", ""),
        )

        if not csrf_token_match:
            raise RuntimeError(
                "Unable to get CSRF token. Try refreshing the Vinted page."
            )

        csrf_token = csrf_token_match.group(1)

    except Exception as e:
        logging.error(
            f"Error while retrieving CSRF token for deletion: {e}"
        )

        raise RuntimeError(f"Security phase failure: {e}")

    # Phase 2: Execute the deletion request

    await send_feedback(f"Phase 2/2: Deleting item {item_id}...")

    try:
        # The correct API endpoint to delete an item is POST to /api/v2/items/{item_id}/delete

        delete_resp = await perform_fetch_via_extension(
            user_id,
            {
                "url": f"https://{domain}/api/v2/items/{item_id}/delete",
                "method": "POST",  # We use POST as specified
                "headers": {
                    "x-csrf-token": csrf_token,
                    "accept": "application/json",
                    "content-type": "application/json",
                },
                "body": "{}",  # Add an empty body for the POST request
            },
        )

        # Check if the request was successful (HTTP 2xx codes)

        if delete_resp.get("http_status_code", 500) >= 400:
            raise RuntimeError(
                f"Vinted returned an error: {delete_resp.get('raw_response_body', 'No details')}"
            )

    except Exception as e:
        logging.error(f"Error during item deletion {item_id}: {e}")

        raise RuntimeError(f"Unable to complete deletion: {e}")

    # If everything went well, return a success message.

    await send_feedback("✅ Deletion completed successfully!")
    return {
        "status": "success",
        "message": f"Item {item_id} deleted successfully!",
    }


async def workflow_get_plan_and_usage(user_id: str, params: dict) -> dict:
    """
    Fetches the user's current plan, their limits, and their daily usage counts
    for all limited features.
    """
    try:
        # 1. Get the user's full permissions object, which includes their limits
        permissions = await get_user_permissions(user_id)
        plan_limits = permissions.get("limits", {})

        # 2. Get the daily usage counts from the database
        start_of_day = datetime.now(timezone.utc).replace(
            hour=0, minute=0, second=0, microsecond=0
        )

        usage_response = (
            supabase.table("usage_logs")
            .select("feature_key")
            .eq("user_id", user_id)
            .gte("created_at", start_of_day.isoformat())
            .execute()
        )

        daily_counts = {}
        if usage_response.data:
            for log in usage_response.data:
                key = log["feature_key"]
                daily_counts[key] = daily_counts.get(key, 0) + 1

        # 3. Combine the limits and usage counts into a structured response
        usage_snapshot = {}
        # Iterate over the limits defined in the user's current plan
        for limit_key, total_limit in plan_limits.items():
            # Example limit_key: "max_REPOST_ITEM_per_day"
            # Extract the feature name: "REPOST_ITEM"
            feature_key = limit_key.replace("max_", "").replace("_per_day", "")

            # Get how many times this feature was used today
            used_today = daily_counts.get(feature_key, 0)

            usage_snapshot[feature_key] = {
                "used": used_today,
                "total": total_limit,  # -1 means unlimited
            }

        # Return the final, combined object for the frontend
        return {
            "plan_name": permissions.get("plan_name"),
            "is_plan_active": permissions.get("is_plan_active"),
            "usage_snapshot": usage_snapshot,
        }

    except Exception as e:
        logger.error(f"Error fetching plan and usage for user {user_id}: {e}")
        return {"error": "Could not retrieve plan and usage data."}
    

# --- NEW, SIMPLIFIED DATA PROCESSOR ---
def vinted_offers_processor(raw_data: str, domain: str = "www.vinted.it") -> list[Dict[str, Any]]:
    """
    Processes the raw JSON response from a single /conversations/{id} endpoint
    and extracts a clean list of offers found in its messages, now with more detail.
    """
    try:
        logger.debug("🔍 Processing conversation data for offers...")
        
        # Validate input data
        if not raw_data or raw_data.strip() == "":
            logger.warning("⚠️ Empty or None raw_data received in vinted_offers_processor")
            return []

        
        # Try to parse JSON
        try:
            data = json.loads(raw_data)
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON decode error in vinted_offers_processor: {e}")
            logger.debug(f"🔍 Raw data received: {raw_data[:200]}...")  # Log first 200 chars
            return []
        
        # Validate data structure
        if not isinstance(data, dict):
            logger.error(f"❌ Expected dict but got {type(data)} in vinted_offers_processor")
            return []
        
        conversation = data.get("conversation", {})
        if not conversation:
            logger.warning("⚠️ No conversation data found in response")
            return []
            
        conv_id = conversation.get("id", "unknown")
        logger.debug(f"📋 Processing conversation {conv_id}")

        # Get general info that applies to all offers in this conversation
        transaction_info = conversation.get("transaction", {})

        #if transaction is null, return empty list
        if not transaction_info:
            return []

        item_title = transaction_info.get("item_title", "Unknown Item")
        opposite_user = conversation.get("opposite_user", {})
        if not isinstance(opposite_user, dict):
            logger.warning(f"⚠️ Invalid opposite_user structure: {type(opposite_user)}")
            opposite_user = {}
            
        offer_maker_username = opposite_user.get("login", "unknown_user")
        
        logger.debug(f"📦 Item: {item_title}, Offer maker: {offer_maker_username}")

        processed_offers = []
        messages = conversation.get("messages", [])
        if not isinstance(messages, list):
            logger.warning(f"⚠️ Invalid messages structure: {type(messages)}")
            messages = []
            
        logger.debug(f"💬 Total messages in conversation: {len(messages)}")

        offer_messages = 0
        pending_offers = 0
        
        for message in messages:
            if not isinstance(message, dict):
                logger.warning(f"⚠️ Invalid message structure: {type(message)}")
                continue
                
            if message.get("entity_type") == "offer_request_message":
                offer_messages += 1
                offer_entity = message.get("entity", {})
                if not isinstance(offer_entity, dict):
                    logger.warning(f"⚠️ Invalid offer_entity structure: {type(offer_entity)}")
                    continue
                    
                status = offer_entity.get("status_title")
                logger.debug(f"💰 Found offer message with status: {status}")

                # We only want to see offers that are pending
                if status == "In sospeso":  # "Pending"
                    pending_offers += 1
                    # NEW: Calculate "time ago"
                    offer_timestamp = message.get("created_at_ts")
                    time_ago = format_time_ago(offer_timestamp) if offer_timestamp else "Unknown time"

                    offer_data = {
                        # Existing fields
                        "conversation_id": conversation.get("id"),
                        "transaction_id": offer_entity.get("transaction_id"),
                        "offer_id": offer_entity.get("offer_request_id"),
                        "item_title": item_title,
                        "offer_amount": offer_entity.get("price"),
                        "currency": offer_entity.get("currency"),
                        "status": offer_entity.get("status_title"),
                        "offer_title": offer_entity.get("title"),
                        # --- NEWLY ADDED FIELDS ---
                        "offer_from_user": offer_maker_username,
                        "time_ago": time_ago,
                        "original_item_price": offer_entity.get("original_price"),
                        "domain": domain,  # Add domain for frontend URL construction
                    }
                    
                    logger.debug(f"✅ Added pending offer: {offer_data}")
                    processed_offers.append(offer_data)

        logger.debug(f"📊 Conversation {conv_id} summary: {offer_messages} offer messages, {pending_offers} pending offers")
        return processed_offers
        
    except Exception as e:
        logger.error(f"❌ Unexpected error in vinted_offers_processor: {e}")
        logger.exception("🔍 Full traceback:")
        return []


# --- NEW, SIMPLIFIED WORKFLOW ---
async def workflow_get_vinted_offers(user_id: str, params: dict) -> dict:
    """
    Fetches all conversations from the user's inbox, then fetches the
    details for each to find embedded offers.
    """
    logger.info(f"🔍 Starting workflow_get_vinted_offers for user {user_id}")
    
    domain = get_active_domain(user_id, params)
    logger.info(f"🌐 Using domain: {domain}")
    
    all_conversations_summary = []
    page = 1
    MAX_PAGES = 5

    # Stage 1: Fetch all conversation summaries from the inbox
    logger.info("📥 Stage 1: Fetching conversations from inbox...")
    while page <= MAX_PAGES:
        inbox_url = f"https://{domain}/api/v2/inbox?page={page}&per_page=50"
        logger.info(f"📋 Fetching inbox page {page}: {inbox_url}")
        try:
            resp = await perform_fetch_via_extension(
                user_id, {"url": inbox_url, "method": "GET"}
            )
            logger.info(f"📊 Inbox page {page} response status: {resp.get('http_status_code', 'UNKNOWN')}")
            
            data = json.loads(resp.get("raw_response_body", "{}"))
            conversations_on_page = data.get("conversations", [])
            logger.info(f"💬 Found {len(conversations_on_page)} conversations on page {page}")

            if not conversations_on_page:
                logger.info(f"📝 No more conversations found on page {page}, stopping")
                break

            all_conversations_summary.extend(conversations_on_page)

            pagination = data.get("pagination", {})
            current_page = pagination.get("current_page", page)
            total_pages = pagination.get("total_pages", 1)
            logger.info(f"📄 Pagination: {current_page}/{total_pages}")
            
            if current_page >= total_pages:
                logger.info("✅ Reached last page")
                break

            page += 1
            await asyncio.sleep(0.5)
        except Exception as e:
            logger.error(f"❌ Error fetching inbox page {page} for user {user_id}: {e}")
            break

    conversation_ids = [
        conv.get("id") for conv in all_conversations_summary if conv.get("id")
    ]
    
    logger.info(f"📋 Total conversations found: {len(all_conversations_summary)}")
    logger.info(f"🔗 Valid conversation IDs: {len(conversation_ids)}")

    if not conversation_ids:
        logger.warning("⚠️ No conversation IDs found, returning empty offers")
        return {"offers": []}

    # Stage 2: Concurrently fetch details for each conversation
    logger.info("📞 Stage 2: Fetching conversation details...")
    tasks = []
    for conv_id in conversation_ids:
        # We need an inner async function to properly use asyncio.gather with arguments
        async def fetch_and_process(cid):
            try:
                logger.debug(f"🔍 Fetching conversation {cid}")
                # Use the new /conversations/{id} endpoint
                response = await perform_fetch_via_extension(
                    user_id,
                    {
                        "url": f"https://{domain}/api/v2/conversations/{cid}",
                        "method": "GET",
                    },
                )
                if response and response.get("raw_response_body"):
                    raw_body = response["raw_response_body"]
                    if raw_body and raw_body.strip():
                        offers = vinted_offers_processor(raw_body, domain)
                        logger.debug(f"💰 Found {len(offers)} offers in conversation {cid}")
                        return offers
                    else:
                        logger.warning(f"⚠️ Empty raw_response_body for conversation {cid}")
                        return []
                else:
                    logger.warning(f"⚠️ Invalid response structure for conversation {cid}: {response}")
                    return []
            except Exception as e:
                logger.error(f"❌ Error processing conversation {cid}: {e}")
                # This will catch errors for conversations that fail to load, which is fine
                return None
            return None

        tasks.append(fetch_and_process(conv_id))

    # Run all API calls in parallel
    logger.info(f"🚀 Running {len(tasks)} conversation fetch tasks in parallel...")
    results = await asyncio.gather(*tasks)

    # Stage 3: Process the final results
    logger.info("📊 Stage 3: Processing results...")
    all_active_offers = []
    successful_conversations = 0
    failed_conversations = 0
    
    for offer_list in results:
        if offer_list:  # offer_list is the list of offers from one conversation
            all_active_offers.extend(offer_list)
            successful_conversations += 1
        else:
            failed_conversations += 1

    logger.info(f"✅ Successfully processed: {successful_conversations} conversations")
    logger.info(f"❌ Failed to process: {failed_conversations} conversations")
    logger.info(f"💰 Total offers found: {len(all_active_offers)}")

    # 🚀 SEND PROGRESSIVE UPDATE TO FRONTEND (Final page)
    try:
        await manager.send_to_user_react(
            user_id,
            {
                "type": "VINTED_OFFERS_PAGE",
                "workflow_key": "GET_VINTED_OFFERS",
                "page_number": 1,
                "offers": all_active_offers,
                "pagination": {
                    "current_page": 1,
                    "total_pages": 1,
                    "is_last_page": True,
                    "total_loaded": len(all_active_offers)
                }
            }
        )
        logger.info(f"📤 Sent final page with {len(all_active_offers)} offers to frontend")
    except Exception as e:
        logger.error(f"❌ Failed to send offers page to frontend: {e}")

    # Still return the traditional result for backward compatibility
    result = {"offers": all_active_offers, "conversations_scanned": len(conversation_ids)}
    logger.info(f"🎯 Final result: {result}")
    return result

# --- NEW WORKFLOW: ACCEPT OFFER ---
async def workflow_accept_offer(user_id: str, params: dict) -> dict:
    """
    Accepts a specific offer on Vinted.
    Requires transaction_id and offer_id.
    """
    transaction_id = params.get("transaction_id")
    offer_id = params.get("offer_id")

    if not transaction_id or not offer_id:
        raise ValueError("Transaction ID and Offer ID are required to accept an offer.")

    domain = get_active_domain(user_id, params)

    # Step 1: Get a CSRF token
    csrf_token = await _get_vinted_csrf_token(user_id, domain)

    # Step 2: Build the URL and send the POST request via the extension
    accept_url = f"https://{domain}/api/v2/transactions/{transaction_id}/offer_requests/{offer_id}/accept"

    # This is a POST request, but it doesn't need a body. The action is in the URL.
    # We must send the CSRF token in the headers.
    headers = {
        "x-csrf-token": csrf_token,
        "accept": "application/json",
        "content-type": "application/json",
    }

    response = await perform_fetch_via_extension(
        user_id, {"url": accept_url, "method": "PUT", "headers": headers}
    )

    if response.get("http_status_code", 500) < 400:
        return {
            "status": "success",
            "message": f"Offer {offer_id} accepted successfully.",
        }
    else:
        # Try to parse an error message from Vinted's response
        error_details = response.get("raw_response_body", "{}")
        raise RuntimeError(
            f"Failed to accept offer. Vinted responded with: {error_details}"
        )


# --- NEW WORKFLOW: DECLINE OFFER ---
async def workflow_decline_offer(user_id: str, params: dict) -> dict:
    """
    Declines a specific offer on Vinted.
    Requires transaction_id and offer_id.
    """
    transaction_id = params.get("transaction_id")
    offer_id = params.get("offer_id")

    if not transaction_id or not offer_id:
        raise ValueError(
            "Transaction ID and Offer ID are required to decline an offer."
        )

    domain = get_active_domain(user_id, params)

    # Step 1: Get a CSRF token
    csrf_token = await _get_vinted_csrf_token(user_id, domain)
    decline_url = f"https://{domain}/api/v2/transactions/{transaction_id}/offer_requests/{offer_id}/reject"

    # ADDED 'content-type' header
    headers = {
        "x-csrf-token": csrf_token,
        "accept": "application/json",
        "content-type": "application/json",
    }

    response = await perform_fetch_via_extension(
        user_id, {"url": decline_url, "method": "PUT", "headers": headers, "body": "{}"}
    )

    if response.get("http_status_code", 500) < 400:
        return {
            "status": "success",
            "message": f"Offer {offer_id} declined successfully.",
        }
    else:
        error_details = response.get("raw_response_body", "{}")
        raise RuntimeError(
            f"Failed to decline offer. Vinted responded with: {error_details}"
        )
    
# --- NEW WORKFLOW: COUNTER OFFER ---
async def workflow_counter_offer(user_id: str, params: dict) -> dict:
    """
    Sends a counter offer on Vinted.
    Requires transaction_id, new_price, and currency.
    """
    transaction_id = params.get("transaction_id")
    new_price = params.get("new_price")
    currency = params.get("currency", "EUR")  # Default to EUR, but allow override

    if not transaction_id or not new_price:
        raise ValueError(
            "Transaction ID and a new price are required to make a counter offer."
        )

    try:
        # Validate that new_price can be converted to a string format Vinted expects
        price_str = f"{float(new_price):.2f}"
    except (ValueError, TypeError):
        raise ValueError("The new price must be a valid number.")

    domain = get_active_domain(user_id, params)

    # Step 1: Get a fresh CSRF token
    csrf_token = await _get_vinted_csrf_token(user_id, domain)

    # Step 2: Build the URL, headers, and the request body
    counter_offer_url = f"https://{domain}/api/v2/transactions/{transaction_id}/offers"

    headers = {
        "x-csrf-token": csrf_token,
        "accept": "application/json",
        "content-type": "application/json",
    }

    # This is the JSON payload Vinted expects
    payload = {"offer": {"price": price_str, "currency": currency}}

    # Step 3: Send the POST request via the extension
    response = await perform_fetch_via_extension(
        user_id,
        {
            "url": counter_offer_url,
            "method": "POST",
            "headers": headers,
            "body": json.dumps(payload),  # Convert the Python dict to a JSON string
        },
    )

    if response.get("http_status_code", 500) < 400:
        return {
            "status": "success",
            "message": f"Counter offer of {price_str} {currency} sent successfully.",
        }
    else:
        error_details = response.get("raw_response_body", "{}")
        raise RuntimeError(
            f"Failed to send counter offer. Vinted responded with: {error_details}"
        )
    

# --- NEW DATA PROCESSOR ---
def follower_notification_processor(
    notification: Dict[str, Any],
) -> Optional[Dict[str, Any]]:
    """
    Processes a single notification object. If it's a 'new follower' event,
    it returns a clean dictionary with the date and username. Otherwise, returns None.
    """
    if notification.get("entry_type") != 30:
        return None

    try:
        # Extract the username from the body string, e.g., "armangralnew ti segue"
        body = notification.get("body", "")
        username = body.split(" ")[0]

        # Extract the date part from the timestamp, e.g., "2025-07-21"
        timestamp_str = notification.get("updated_at", "")
        date_str = timestamp_str.split("T")[0]

        return {"date": date_str, "username": username}
    except (IndexError, TypeError):
        # In case the body or timestamp format is unexpected
        return None
    

# --- UPDATED WORKFLOW WITH WEEKLY AND MONTHLY STATS ---
async def workflow_get_follower_stats(user_id: str, params: dict) -> dict:
    """
    Generates follower stats by querying our own database, which is populated
    by a daily background job.
    """
    # Fetch all follower events for this user from our database
    db_response = (
        supabase.table("follower_events")
        .select("follower_username, followed_at")
        .eq("user_id", user_id)
        .execute()
    )

    if not db_response.data:
        return {"daily_stats": [], "weekly_stats": [], "monthly_stats": []}

    # The aggregation logic is the same as before, but it uses data from our DB
    followers_by_day = defaultdict(list)
    followers_by_week = defaultdict(list)
    followers_by_month = defaultdict(list)

    for event in db_response.data:
        # The timestamp from the DB will be a full timestamp
        event_date_obj = datetime.fromisoformat(event["followed_at"]).date()

        day_key = event_date_obj.isoformat()
        week_key = event_date_obj.strftime("%G-%V")
        month_key = event_date_obj.strftime("%Y-%m")

        followers_by_day[day_key].append(event["follower_username"])
        followers_by_week[week_key].append(event["follower_username"])
        followers_by_month[month_key].append(event["follower_username"])

    # The formatting logic is exactly the same as before
    # ... (copy-paste the formatting logic from the previous answer) ...
    daily_stats = [...]
    weekly_stats = [...]
    monthly_stats = [...]

    return {
        "daily_stats": daily_stats,
        "weekly_stats": weekly_stats,
        "monthly_stats": monthly_stats,
    }


# --- NEW WORKFLOW: GET USER PROFILE ---
async def workflow_get_user_profile(user_id: str, params: dict) -> dict:
    """
    Fetches the user's profile information sequentially. First from our DB,
    then from Vinted's /users/current endpoint.
    """
    domain = get_active_domain(user_id, params)

    try:
        # Step 1: Get subscription permissions from our database. Await it immediately.
        logger.info(f"Fetching permissions for user {user_id}...")
        permissions = await get_user_permissions(user_id)
        logger.info(f"Permissions fetched for user {user_id}.")

        # Step 2: Get Vinted profile data via the extension. Await it immediately.
        logger.info(f"Fetching Vinted profile for user {user_id}...")
        vinted_profile_url = f"https://{domain}/api/v2/users/current"
        vinted_response = await perform_fetch_via_extension(
            user_id,
            {
                "url": vinted_profile_url,
                "method": "GET",
                "headers": {"accept": "application/json"},
            },
        )
        logger.info(f"Vinted profile fetched for user {user_id}.")

    except Exception as e:
        logger.error(f"Error during sequential fetch for user profile {user_id}: {e}")
        raise RuntimeError(f"Could not retrieve all user profile details.")

    # Now, process the results from both steps
    try:
        vinted_data = json.loads(vinted_response.get("raw_response_body", "{}"))
        vinted_user_data = vinted_data.get("user", {})
    except (json.JSONDecodeError, AttributeError):
        raise RuntimeError("Failed to parse user profile from Vinted.")

    # Extract details (this part is the same)
    vinted_username = vinted_user_data.get("login")
    vinted_id = vinted_user_data.get("id")
    vinted_avatar_url = vinted_user_data.get("photo", {}).get("url")

    # Combine everything (this part is the same)
    profile_data = {
        "user_id": user_id,
        "vinted_id": vinted_id,
        "vinted_username": vinted_username,
        "vinted_avatar_url": vinted_avatar_url,
        "subscription": {
            "plan_id": permissions.get("plan_id"),
            "plan_name": permissions.get("plan_name"),
            "is_active": permissions.get("is_plan_active"),
        },
    }

    return profile_data


# --- WORKFLOW: FOLLOW USER ---
async def workflow_follow_unified(user_id: str, params: dict) -> dict:
    """
    WORKFLOW UNIFICATO per gestire TUTTE le operazioni follow/unfollow.
    Questo sostituisce tutti i precedenti workflow follow separati.
    
    Determina l'azione basandosi sul workflow_key e gestisce tutto internamente.
    1 Feature = 1 Workflow = Questo workflow gestisce TUTTO.
    """
    
    # Ottieni il workflow_key per determinare l'azione
    workflow_key = params.get("workflow_key", "FOLLOW_USER")
    
    logger.info(f"👥 Follow workflow unificato - User: {user_id}, WorkflowKey: {workflow_key}")
    
    try:
        if workflow_key == "FOLLOW_USER":
            return await _follow_single_user(user_id, params)
        
        elif workflow_key == "UNFOLLOW_USER":
            return await _unfollow_single_user(user_id, params)
        
        elif workflow_key == "START_FOLLOW_AUTOMATION":
            return await _start_follow_automation(user_id, params)
        
        elif workflow_key == "STOP_FOLLOW_AUTOMATION":
            return await _stop_follow_automation(user_id, params)
        
        elif workflow_key == "GET_FOLLOW_AUTOMATION_STATUS":
            return await _get_follow_automation_status(user_id, params)
        
        elif workflow_key == "GET_FOLLOW_ACTIVITY_LOGS":
            return await _get_follow_activity_logs(user_id, params)
        
        elif workflow_key == "GET_FOLLOW_USAGE":
            return await _get_follow_usage(user_id, params)
        
        else:
            return {
                "success": False,
                "error": f"Unknown follow action: {workflow_key}"
            }
            
    except Exception as e:
        logger.error(f"❌ Error in unified follow workflow {workflow_key}: {e}")
        return {
            "success": False,
            "error": str(e)
        }


async def _follow_single_user(user_id: str, params: dict) -> dict:
    """Segui un singolo utente."""
    target_user_id = params.get("target_user_id")
    if not target_user_id:
        return {"success": False, "error": "target_user_id richiesto"}
    
    # TODO: Implementare logica follow singolo
    logger.info(f"👥 Following user {target_user_id} for {user_id}")
    return {"success": True, "message": f"Followed user {target_user_id}"}


async def _unfollow_single_user(user_id: str, params: dict) -> dict:
    """Smetti di seguire un singolo utente."""
    target_user_id = params.get("target_user_id")
    if not target_user_id:
        return {"success": False, "error": "target_user_id richiesto"}
    
    # TODO: Implementare logica unfollow singolo
    logger.info(f"👥 Unfollowing user {target_user_id} for {user_id}")
    return {"success": True, "message": f"Unfollowed user {target_user_id}"}


async def _start_follow_automation(user_id: str, params: dict) -> dict:
    """Avvia automazione follow (gestisce internamente la persistenza)."""
    # TODO: Implementare avvio automazione con background task gestito internamente
    logger.info(f"� Starting follow automation for {user_id}")
    return {"success": True, "message": "Follow automation started"}


async def _stop_follow_automation(user_id: str, params: dict) -> dict:
    """Ferma automazione follow."""
    # TODO: Implementare stop automazione
    logger.info(f"🛑 Stopping follow automation for {user_id}")
    return {"success": True, "message": "Follow automation stopped"}


async def _get_follow_automation_status(user_id: str, params: dict) -> dict:
    """Ottieni stato automazione follow."""
    # TODO: Implementare controllo stato
    logger.info(f"📊 Getting follow automation status for {user_id}")
    return {"success": True, "status": "inactive", "message": "No automation running"}


async def _get_follow_activity_logs(user_id: str, params: dict) -> dict:
    """Ottieni log attività follow."""
    # TODO: Implementare recupero log
    logger.info(f"📜 Getting follow activity logs for {user_id}")
    return {"success": True, "logs": [], "message": "No logs available"}


async def _get_follow_usage(user_id: str, params: dict) -> dict:
    """Ottieni statistiche utilizzo follow."""
    # TODO: Implementare statistiche utilizzo
    logger.info(f"📈 Getting follow usage stats for {user_id}")
    return {"success": True, "usage": {"daily_count": 0, "limit": 100}}
    """
    Segue un utente specifico su Vinted utilizzando la logica derivata dal codice JavaScript.
    Implementa controlli di permessi, rate limiting e salvataggio nel database.
    
    Parametri richiesti:
    - target_user_id: L'ID dell'utente Vinted da seguire
    """
    logger.info(f"👥 Avvio workflow_follow_user per user {user_id}")
    
    # 1. Validazione parametri
    target_user_id = params.get("target_user_id")
    if not target_user_id:
        raise ValueError("target_user_id è richiesto")
    
    # 2. Controllo permessi
    permissions = await get_user_permissions(user_id)
    if not permissions.get("features", {}).get("FOLLOW_USER", False):
        raise PermissionDeniedError("Non hai i permessi per seguire utenti")
    
    # 3. Controllo limiti giornalieri
    daily_limit = permissions.get("limits", {}).get("max_FOLLOW_USER_per_day", 0)
    if daily_limit > 0:  # -1 significa illimitato
        start_of_day = datetime.now(timezone.utc).replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        
        usage_response = (
            supabase.table("usage_logs")
            .select("*")
            .eq("user_id", user_id)
            .eq("feature_key", "FOLLOW_USER")
            .gte("created_at", start_of_day.isoformat())
            .execute()
        )
        
        used_today = len(usage_response.data) if usage_response.data else 0
        
        if used_today >= daily_limit:
            return {
                "success": False,
                "error": f"Limite giornaliero raggiunto. Puoi seguire {daily_limit} utenti al giorno. Riprova domani.",
                "used_today": used_today,
                "daily_limit": daily_limit
            }
    
    # 4. Ottieni informazioni utente Vinted
    vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
    if not (vinted_user_info and vinted_user_info.get("id")):
        try:
            login_result = await workflow_check_vinted_login(user_id, params)
            if not login_result.get("is_logged_in"):
                raise ValueError("Utente non loggato su Vinted. Effettua il login prima di seguire utenti.")
            vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
            if not (vinted_user_info and vinted_user_info.get("id")):
                raise ValueError("Informazioni utente Vinted non disponibili dopo il login.")
        except Exception as e:
            raise ValueError(f"Impossibile verificare lo stato del login: {str(e)}")
    
    vinted_user_id = str(vinted_user_info["id"])
    domain = get_active_domain(user_id, params)
    
    # 5. Verifica se l'utente è già stato seguito in precedenza
    existing_follow = (
        supabase.table("followed_users")
        .select("id")
        .eq("user_id", user_id)
        .eq("vinted_user_id", vinted_user_id)
        .eq("followed_vinted_user_id", str(target_user_id))
        .execute()
    )
    
    if existing_follow.data:
        logger.info(f"👥 Utente {target_user_id} già seguito in precedenza, skip")
        return {
            "success": True,
            "message": f"Utente {target_user_id} già seguito in precedenza",
            "target_user_id": target_user_id,
            "action": "follow",
            "skipped": True  # Indica che è stato saltato
        }
    
    try:
        # 6. Ottieni token CSRF (necessario per tutte le azioni POST su Vinted)
        csrf_token = await _get_vinted_csrf_token(user_id, domain)
        
        # 7. Esegui la richiesta di follow tramite API Vinted
        # Usa l'endpoint toggle che deriva dal codice JavaScript originale
        follow_payload = {"user_id": int(target_user_id)}
        
        follow_response = await perform_fetch_via_extension(
            user_id, 
            {
                "url": f"https://{domain}/api/v2/followed_users/toggle",
                "method": "POST",
                "headers": {
                    "Content-Type": "application/json",
                    "x-csrf-token": csrf_token,
                    "Accept": "application/json"
                },
                "body": json.dumps(follow_payload)
            }
        )
        
        http_status = follow_response.get("http_status_code", 500)
        response_body = follow_response.get("raw_response_body", "{}")
        
        # 8. Gestione della risposta secondo i codici del JS originale
        if http_status == 200:
            try:
                response_data = json.loads(response_body)
                
                # Registra l'uso della feature
                supabase.table("usage_logs").insert({
                    "user_id": user_id,
                    "feature_key": "FOLLOW_USER",
                    "created_at": datetime.now(timezone.utc).isoformat()
                }).execute()
                
                # Salva nel database il follow (solo se nuovo)
                supabase.table("followed_users").insert({
                    "user_id": user_id,
                    "vinted_user_id": vinted_user_id,
                    "followed_vinted_user_id": str(target_user_id),
                    "followed_at": datetime.now(timezone.utc).isoformat()
                }).execute()
                
                logger.info(f"✅ Utente {target_user_id} seguito con successo")
                
                return {
                    "success": True,
                    "message": f"Utente {target_user_id} seguito con successo",
                    "target_user_id": target_user_id,
                    "action": "follow",
                    "vinted_response": response_data
                }
                
            except json.JSONDecodeError:
                logger.warning("Risposta Vinted non è JSON valido, ma status 200")
                return {
                    "success": True,
                    "message": f"Utente {target_user_id} seguito (risposta non JSON)",
                    "target_user_id": target_user_id,
                    "action": "follow"
                }
        
        elif http_status == 429:
            # Rate limit raggiunto
            return {
                "success": False,
                "error": "Rate limit raggiunto. Riprova tra 20-30 secondi.",
                "code": 429,
                "target_user_id": target_user_id
            }
        
        elif http_status in [401, 403]:
            # Token CSRF non valido o scaduto
            return {
                "success": False,
                "error": "Token di autenticazione non valido. Riapri Vinted in un'altra tab e riprova.",
                "code": http_status,
                "target_user_id": target_user_id
            }
        
        elif http_status == 524:
            # Timeout del server Vinted
            return {
                "success": False,
                "error": "Timeout del server Vinted. Riprova più tardi.",
                "code": 524,
                "target_user_id": target_user_id
            }
        
        else:
            # Altri errori
            try:
                error_response = json.loads(response_body)
                error_msg = error_response.get("message", f"Errore HTTP {http_status}")
            except:
                error_msg = f"Errore HTTP {http_status}"
            
            return {
                "success": False,
                "error": f"Errore durante il follow: {error_msg}",
                "code": http_status,
                "target_user_id": target_user_id
            }
            
    except Exception as e:
        logger.error(f"❌ Errore in workflow_follow_user: {e}")
        return {
            "success": False,
            "error": f"Errore interno: {str(e)}",
            "target_user_id": target_user_id
        }








# === END FOLLOW AUTOMATION WORKFLOWS ===


# === AUTO MESSAGES WORKFLOWS ===

async def workflow_get_auto_message_settings(user_id: str, params: dict) -> dict:
    """
    Get auto message settings and messages for the user.
    """
    try:
        # For now, return mock data since we don't have a database table yet
        # TODO: Implement actual database query when auto_messages table is created
        
        return {
            "is_enabled": False,
            "messages": []
        }
        
    except Exception as e:
        logger.error(f"Error getting auto message settings for user {user_id}: {e}")
        return {"error": f"Failed to get auto message settings: {str(e)}"}


async def workflow_toggle_auto_messages(user_id: str, params: dict) -> dict:
    """
    Toggle auto messages on/off for the user.
    """
    try:
        enabled = params.get("enabled", False)
        
        # TODO: Implement actual database update when auto_messages_settings table is created
        # For now, just return success
        
        return {
            "success": True,
            "is_enabled": enabled,
            "message": f"Auto messages {'enabled' if enabled else 'disabled'} successfully"
        }
        
    except Exception as e:
        logger.error(f"Error toggling auto messages for user {user_id}: {e}")
        return {"error": f"Failed to toggle auto messages: {str(e)}"}


async def workflow_create_auto_message(user_id: str, params: dict) -> dict:
    """
    Create a new auto message.
    """
    try:
        # Check usage limits
        permissions = await get_user_permissions(user_id)
        daily_limit = permissions.get("limits", {}).get("max_CREATE_AUTO_MESSAGE_per_day", 5)
        
        if daily_limit > 0:  # -1 means unlimited
            # Check how many messages created today
            start_of_day = datetime.now(timezone.utc).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            
            usage_response = (
                supabase.table("usage_logs")
                .select("*")
                .eq("user_id", user_id)
                .eq("feature_key", "CREATE_AUTO_MESSAGE")
                .gte("created_at", start_of_day.isoformat())
                .execute()
            )
            
            used_today = len(usage_response.data) if usage_response.data else 0
            
            if used_today >= daily_limit:
                return {
                    "error": f"Daily limit reached. You can create {daily_limit} messages per day. Try again tomorrow."
                }
        
        title = params.get("title", "").strip()
        content = params.get("content", "").strip()
        is_active = params.get("is_active", True)
        
        if not title or not content:
            return {"error": "Title and content are required"}
        
        # TODO: Implement actual database insert when auto_messages table is created
        # For now, log the usage
        supabase.table("usage_logs").insert({
            "user_id": user_id,
            "feature_key": "CREATE_AUTO_MESSAGE",
            "created_at": datetime.now(timezone.utc).isoformat()
        }).execute()
        
        return {
            "success": True,
            "message": "Auto message created successfully",
            "data": {
                "id": f"msg_{int(datetime.now().timestamp())}",
                "title": title,
                "content": content,
                "is_active": is_active,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error creating auto message for user {user_id}: {e}")
        return {"error": f"Failed to create auto message: {str(e)}"}


async def workflow_update_auto_message(user_id: str, params: dict) -> dict:
    """
    Update an existing auto message.
    """
    try:
        message_id = params.get("id")
        title = params.get("title", "").strip()
        content = params.get("content", "").strip()
        is_active = params.get("is_active", True)
        
        if not message_id:
            return {"error": "Message ID is required"}
        
        if not title or not content:
            return {"error": "Title and content are required"}
        
        # TODO: Implement actual database update when auto_messages table is created
        
        return {
            "success": True,
            "message": "Auto message updated successfully",
            "data": {
                "id": message_id,
                "title": title,
                "content": content,
                "is_active": is_active,
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error updating auto message for user {user_id}: {e}")
        return {"error": f"Failed to update auto message: {str(e)}"}


async def workflow_delete_auto_message(user_id: str, params: dict) -> dict:
    """
    Delete an auto message.
    """
    try:
        message_id = params.get("message_id")
        
        if not message_id:
            return {"error": "Message ID is required"}
        
        # TODO: Implement actual database delete when auto_messages table is created
        
        return {
            "success": True,
            "message": "Auto message deleted successfully"
        }
        
    except Exception as e:
        logger.error(f"Error deleting auto message for user {user_id}: {e}")
        return {"error": f"Failed to delete auto message: {str(e)}"}


async def workflow_send_manual_message(user_id: str, params: dict) -> dict:
    """
    Send a manual message to a user who liked an item.
    """
    try:
        # Check usage limits
        permissions = await get_user_permissions(user_id)
        daily_limit = permissions.get("limits", {}).get("max_SEND_MANUAL_MESSAGE_per_day", 20)
        
        if daily_limit > 0:  # -1 means unlimited
            # Check how many manual messages sent today
            start_of_day = datetime.now(timezone.utc).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            
            usage_response = (
                supabase.table("usage_logs")
                .select("*")
                .eq("user_id", user_id)
                .eq("feature_key", "SEND_MANUAL_MESSAGE")
                .gte("created_at", start_of_day.isoformat())
                .execute()
            )
            
            used_today = len(usage_response.data) if usage_response.data else 0
            
            if used_today >= daily_limit:
                return {
                    "error": f"Daily limit reached. You can send {daily_limit} manual messages per day. Try again tomorrow."
                }
        
        like_id = params.get("like_id")
        message_content = params.get("message_content", "").strip()
        
        if not like_id or not message_content:
            return {"error": "Like ID and message content are required"}
        
        # TODO: Implement actual message sending via Vinted API
        # For now, log the usage
        supabase.table("usage_logs").insert({
            "user_id": user_id,
            "feature_key": "SEND_MANUAL_MESSAGE",
            "created_at": datetime.now(timezone.utc).isoformat()
        }).execute()
        
        return {
            "success": True,
            "message": "Manual message sent successfully"
        }
        
    except Exception as e:
        logger.error(f"Error sending manual message for user {user_id}: {e}")
        return {"error": f"Failed to send manual message: {str(e)}"}


async def workflow_get_auto_message_usage(user_id: str, params: dict) -> dict:
    """
    Get auto message daily usage statistics for the user.
    """
    try:
        # Get user permissions and limits
        permissions = await get_user_permissions(user_id)
        limits = permissions.get("limits", {})
        plan_name = permissions.get("plan_name", "Free")
        
        # Get today's usage from database
        start_of_day = datetime.now(timezone.utc).replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        
        usage_response = (
            supabase.table("usage_logs")
            .select("feature_key")
            .eq("user_id", user_id)
            .gte("created_at", start_of_day.isoformat())
            .execute()
        )
        
        # Count usage per feature
        create_message_used = 0
        send_manual_message_used = 0
        
        if usage_response.data:
            for log in usage_response.data:
                if log["feature_key"] == "CREATE_AUTO_MESSAGE":
                    create_message_used += 1
                elif log["feature_key"] == "SEND_MANUAL_MESSAGE":
                    send_manual_message_used += 1
        
        # Get limits
        create_message_limit = limits.get("max_CREATE_AUTO_MESSAGE_per_day", 5)
        send_manual_message_limit = limits.get("max_SEND_MANUAL_MESSAGE_per_day", 20)
        
        return {
            "create_message_usage": {
                "used": create_message_used,
                "limit": create_message_limit,
                "remaining": create_message_limit - create_message_used if create_message_limit > 0 else -1
            },
            "send_manual_message_usage": {
                "used": send_manual_message_used,
                "limit": send_manual_message_limit,
                "remaining": send_manual_message_limit - send_manual_message_used if send_manual_message_limit > 0 else -1
            },
            "plan_name": plan_name
        }
        
    except Exception as e:
        logger.error(f"❌ Error getting auto message usage stats: {e}")
        raise RuntimeError(f"Failed to get usage stats: {str(e)}")


# === END AUTO MESSAGES WORKFLOWS ===


# --- NEW WORKFLOW: GET VINTED ORDERS ---
async def workflow_get_vinted_orders(user_id: str, params: dict) -> dict:
    """
    Fetches all sold orders from the user's Vinted account using the my_orders endpoint.
    Supports progressive loading - sends each page to frontend as it's fetched.
    """
    logger.info(f"🛒 Starting workflow_get_vinted_orders for user {user_id}")
    
    domain = get_active_domain(user_id, params)
    logger.info(f"🌐 Using domain: {domain}")
    
    all_orders = []
    page = 1
    MAX_PAGES = 20  # Reasonable limit to avoid infinite loops
    total_pages = 1
    
    # Get orders with progressive loading
    logger.info("📦 Fetching orders from my_orders API with progressive loading...")
    
    while page <= MAX_PAGES and page <= total_pages:
        # Use the correct endpoint with proper parameters
        orders_url = f"https://{domain}/api/v2/my_orders?type=sold&status=all&per_page=20&page={page}"
        logger.info(f"📋 Fetching orders page {page}: {orders_url}")
        
        try:
            resp = await perform_fetch_via_extension(
                user_id, 
                {
                    "url": orders_url, 
                    "method": "GET",
                    "headers": {"accept": "application/json"}
                }
            )
            
            logger.info(f"📊 Orders page {page} response status: {resp.get('http_status_code', 'UNKNOWN')}")
            
            if resp.get("http_status_code", 500) != 200:
                logger.warning(f"❌ Failed to fetch orders page {page}: HTTP {resp.get('http_status_code')}")
                break
            
            data = json.loads(resp.get("raw_response_body", "{}"))
            orders_on_page = data.get("my_orders", [])
            logger.info(f"📦 Found {len(orders_on_page)} orders on page {page}")
            
            if not orders_on_page:
                logger.info(f"📝 No more orders found on page {page}, stopping")
                break
            
            # Process orders from this page
            page_orders = []
            for order in orders_on_page:
                try:
                    # Transform the order data to match frontend expectations
                    processed_order = {
                        "id": f"ORD-{order.get('transaction_id', 'unknown')}",
                        "conversation_id": order.get("conversation_id"),
                        "transaction_id": order.get("transaction_id"),
                        "title": order.get("title", "Unknown Item"),
                        "customer": "Vinted User",  # Vinted doesn't expose buyer info for privacy
                        "product": order.get("title", "Unknown Item"),
                        "amount": f"€{order.get('price', {}).get('amount', '0.00')}",
                        "currency": order.get('price', {}).get('currency_code', 'EUR'),
                        "date": order.get("date"),
                        "status": vinted_order_status_mapper(order.get("transaction_user_status")),
                        "vinted_status": order.get("status"),
                        "photo": order.get("photo"),
                        "domain": domain,  # Add domain for frontend URL construction
                        # Keep original data for reference
                        "original_data": order
                    }
                    page_orders.append(processed_order)
                    all_orders.append(processed_order)
                    
                except Exception as e:
                    logger.warning(f"⚠️ Error processing order {order.get('transaction_id', 'unknown')}: {e}")
                    continue
            
            # Get pagination info
            pagination = data.get("pagination", {})
            current_page = pagination.get("current_page", page)
            total_pages = pagination.get("total_pages", 1)
            logger.info(f"📄 Pagination: {current_page}/{total_pages}")
            
            # 🚀 SEND PROGRESSIVE UPDATE TO FRONTEND
            if page_orders:  # Only send if we have orders
                try:
                    await manager.send_to_user_react(
                        user_id,
                        {
                            "type": "VINTED_ORDERS_PAGE",
                            "workflow_key": "GET_VINTED_ORDERS",
                            "page_number": page,
                            "orders": page_orders,
                            "pagination": {
                                "current_page": current_page,
                                "total_pages": total_pages,
                                "is_last_page": current_page >= total_pages,
                                "total_loaded": len(all_orders)
                            }
                        }
                    )
                    logger.info(f"📤 Sent page {page} with {len(page_orders)} orders to frontend")
                except Exception as e:
                    logger.error(f"❌ Failed to send page {page} to frontend: {e}")
            
            # Check if we've reached the last page
            if current_page >= total_pages:
                logger.info("✅ Reached last page")
                break
                
            page += 1
            await asyncio.sleep(0.3)  # Rate limiting
            
        except Exception as e:
            logger.error(f"❌ Error fetching orders page {page} for user {user_id}: {e}")
            break
    
    logger.info(f"📊 Total orders fetched: {len(all_orders)} across {page - 1} pages")
    
    # Final response with all orders
    return {
        "orders": all_orders,
        "total_count": len(all_orders),
        "pages_fetched": page - 1,
        "loading_complete": True
    }


# --- NEW WORKFLOW: GET VINTED NOTIFICATIONS AND PROCESS LIKES ---
def vinted_notifications_processor(raw_data: str) -> dict:
    """
    Processes the raw JSON response from the notifications endpoint
    and extracts useful information including likes (entry_type == 20).
    """
    try:
        logger.debug("🔍 Processing notifications data...")
        
        # Validate input data
        if not raw_data or raw_data.strip() == "":
            logger.warning("⚠️ Empty or None raw_data received in vinted_notifications_processor")
            return {
                "notifications": [],
                "pagination": {},
                "stats": {"total_notifications": 0, "likes_count": 0, "follows_count": 0}
            }
        
        # Try to parse JSON
        try:
            data = json.loads(raw_data)
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON decode error in vinted_notifications_processor: {e}")
            logger.debug(f"🔍 Raw data received: {raw_data[:200]}...")  # Log first 200 chars
            return {
                "notifications": [],
                "pagination": {},
                "stats": {"total_notifications": 0, "likes_count": 0, "follows_count": 0}
            }
        
        # Validate data structure
        if not isinstance(data, dict):
            logger.error(f"❌ Expected dict but got {type(data)} in vinted_notifications_processor")
            return {
                "notifications": [],
                "pagination": {},
                "stats": {"total_notifications": 0, "likes_count": 0, "follows_count": 0}
            }
        
        notifications = data.get("notifications", [])
        if not isinstance(notifications, list):
            logger.warning(f"⚠️ Invalid notifications structure: {type(notifications)}")
            notifications = []
            
        pagination = data.get("pagination", {})
        if not isinstance(pagination, dict):
            logger.warning(f"⚠️ Invalid pagination structure: {type(pagination)}")
            pagination = {}
        
        logger.debug(f"💌 Total notifications found: {len(notifications)}")
        
        processed_notifications = []
        likes_count = 0
        follows_count = 0
        
        for notification in notifications:
            if not isinstance(notification, dict):
                logger.warning(f"⚠️ Invalid notification structure: {type(notification)}")
                continue
                
            try:
                notification_data = {
                    "notification_id": notification.get("id"),
                    "entry_type": notification.get("entry_type"),
                    "subject_id": notification.get("subject_id"),
                    "user_id": notification.get("user_id"),
                    "updated_at": notification.get("updated_at"),
                    "is_read": notification.get("is_read"),
                    "body": notification.get("body"),
                    "link": notification.get("link"),
                    "photo": notification.get("photo"),
                    "photo_type": notification.get("photo_type"),
                    "small_photo_url": notification.get("small_photo_url")
                }
                
                # Extract actor username from body for likes and follows
                body = notification.get("body", "")
                if notification.get("entry_type") == 20:  # Like
                    likes_count += 1
                    if " ti ha messo mi piace" in body:
                        notification_data["actor_username"] = body.split(" ti ha messo mi piace")[0]
                    elif " likes your item" in body:
                        notification_data["actor_username"] = body.split(" likes your item")[0]
                    else:
                        notification_data["actor_username"] = "unknown"
                elif notification.get("entry_type") == 30:  # Follow
                    follows_count += 1
                    if " ti segue" in body:
                        notification_data["actor_username"] = body.split(" ti segue")[0]
                    elif " follows you" in body:
                        notification_data["actor_username"] = body.split(" follows you")[0]
                    else:
                        notification_data["actor_username"] = "unknown"
                
                processed_notifications.append(notification_data)
                
            except Exception as e:
                logger.warning(f"⚠️ Error processing notification {notification.get('id', 'unknown')}: {e}")
                continue
        
        logger.debug(f"💖 Found {likes_count} likes, 👥 {follows_count} follows")
        
        return {
            "notifications": processed_notifications,
            "pagination": pagination,
            "stats": {
                "total_notifications": len(processed_notifications),
                "likes_count": likes_count,
                "follows_count": follows_count
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Unexpected error in vinted_notifications_processor: {e}")
        logger.exception("🔍 Full traceback:")
        return {
            "notifications": [],
            "pagination": {},
            "stats": {"total_notifications": 0, "likes_count": 0, "follows_count": 0}
        }


async def workflow_sync_vinted_notifications(user_id: str, params: dict) -> dict:
    """
    Syncs notifications from Vinted and processes likes/follows.
    This workflow manages the entire process through the extension.
    """
    logger.info(f"� Starting workflow_sync_vinted_notifications for user {user_id}")
    
    domain = get_active_domain(user_id, params)
    logger.info(f"🌐 Using domain: {domain}")
    
    # Get Vinted user info
    vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
    if not (vinted_user_info and vinted_user_info.get("id")):
        try:
            login_result = await workflow_check_vinted_login(user_id, params)
            if not login_result.get("is_logged_in"):
                raise ValueError("User not logged into Vinted. Please log in first.")
            vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
            if not (vinted_user_info and vinted_user_info.get("id")):
                raise ValueError("Vinted user information not available after login check.")
        except Exception as e:
            raise ValueError(f"Unable to verify login status: {str(e)}")
    
    vinted_user_id = str(vinted_user_info["id"])
    
    # Get last processed notification ID from database
    last_notification_response = (
        supabase.table("vinted_sync_status")
        .select("last_notification_id")
        .eq("vinted_user_id", vinted_user_id)
        .eq("user_id", user_id)
        .execute()
    )
    
    last_notification_id = None
    if last_notification_response.data:
        last_notification_id = last_notification_response.data[0].get("last_notification_id")
        logger.info(f"📋 Last processed notification ID: {last_notification_id}")
    
    all_new_data = []
    page = 1
    MAX_PAGES = 20  # Reasonable limit
    per_page = 99
    should_stop = False
    newest_notification_id = None
    
    logger.info("💌 Fetching notifications from Vinted via extension...")
    
    while page <= MAX_PAGES and not should_stop:
        notifications_url = f"https://{domain}/api/v2/notifications?page={page}&per_page={per_page}"
        logger.info(f"📋 Fetching notifications page {page}: {notifications_url}")
        
        try:
            resp = await perform_fetch_via_extension(
                user_id,
                {
                    "url": notifications_url,
                    "method": "GET",
                    "headers": {"accept": "application/json"}
                }
            )
            
            logger.info(f"📊 Notifications page {page} response status: {resp.get('http_status_code', 'UNKNOWN')}")
            
            if resp.get("http_status_code", 500) != 200:
                logger.warning(f"❌ Failed to fetch notifications page {page}: HTTP {resp.get('http_status_code')}")
                break
            
            # Process the response
            processed_data = vinted_notifications_processor(resp.get("raw_response_body", "{}"))
            notifications = processed_data.get("notifications", [])
            
            logger.info(f"💌 Found {len(notifications)} notifications on page {page}")
            
            if not notifications:
                logger.info(f"📝 No more notifications found on page {page}, stopping")
                break
            
            # Store the newest notification ID from the first page
            if page == 1 and notifications:
                newest_notification_id = notifications[0].get("notification_id")
                logger.info(f"🆕 Newest notification ID: {newest_notification_id}")
            
            # Check if we've reached the last processed notification
            for notification in notifications:
                if last_notification_id and notification.get("notification_id") == last_notification_id:
                    logger.info(f"🛑 Reached last processed notification {last_notification_id}, stopping")
                    should_stop = True
                    break
            
            # Filter new notifications (only those we haven't processed)
            new_notifications = []
            for notification in notifications:
                if not last_notification_id or notification.get("notification_id") != last_notification_id:
                    # Convert to database format
                    data_entry = {
                        "vinted_user_id": vinted_user_id,
                        "user_id": user_id,
                        "notification_id": notification.get("notification_id"),
                        "entry_type": notification.get("entry_type"),
                        "occurred_at": notification.get("updated_at"),
                        "notification_body": notification.get("body"),
                        "raw_data": notification,
                        "domain": domain
                    }
                    
                    # Determine data type and add specific fields
                    if notification.get("entry_type") == 20:  # Like
                        data_entry.update({
                            "data_type": "like",
                            "item_id": str(notification.get("subject_id")) if notification.get("subject_id") else None,
                            "item_photo_url": notification.get("small_photo_url"),
                            "actor_username": notification.get("actor_username", "unknown")
                        })
                    elif notification.get("entry_type") == 30:  # Follow
                        data_entry.update({
                            "data_type": "follow",
                            "actor_username": notification.get("actor_username", "unknown")
                        })
                    else:
                        data_entry.update({
                            "data_type": "notification",
                            "item_id": str(notification.get("subject_id")) if notification.get("subject_id") else None
                        })
                    
                    new_notifications.append(data_entry)
            
            all_new_data.extend(new_notifications)
            logger.info(f"� Found {len(new_notifications)} new items on page {page}")
            
            # Check pagination
            pagination = processed_data.get("pagination", {})
            current_page = pagination.get("current_page", page)
            total_pages = pagination.get("total_pages", 1)
            
            if current_page >= total_pages:
                logger.info("✅ Reached last page")
                break
            
            page += 1
            await asyncio.sleep(0.3)  # Rate limiting
            
        except Exception as e:
            logger.error(f"❌ Error fetching notifications page {page} for user {user_id}: {e}")
            break
    
    logger.info(f"� Total new data found: {len(all_new_data)} across {page - 1} pages")
    
    # Save new data to database
    saved_count = 0
    if all_new_data:
        logger.info("💾 Saving new data to database...")
        for data_entry in all_new_data:
            try:
                # Check if this notification already exists
                existing_check = (
                    supabase.table("vinted_user_data")
                    .select("id")
                    .eq("vinted_user_id", vinted_user_id)
                    .eq("notification_id", data_entry["notification_id"])
                    .eq("data_type", data_entry["data_type"])
                    .execute()
                )
                
                if not existing_check.data:
                    supabase.table("vinted_user_data").insert(data_entry).execute()
                    saved_count += 1
                    
            except Exception as e:
                logger.error(f"❌ Error saving data entry {data_entry.get('notification_id')}: {e}")
                continue
    
    # Update sync status
    if newest_notification_id:
        try:
            # Check if sync status record exists
            existing_sync = (
                supabase.table("vinted_sync_status")
                .select("id")
                .eq("vinted_user_id", vinted_user_id)
                .eq("user_id", user_id)
                .execute()
            )
            
            sync_data = {
                "last_notification_id": newest_notification_id,
                "last_sync_at": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
            
            if existing_sync.data:
                # Update existing record
                current_count = (
                    supabase.table("vinted_sync_status")
                    .select("sync_count")
                    .eq("vinted_user_id", vinted_user_id)
                    .eq("user_id", user_id)
                    .execute()
                ).data[0].get("sync_count", 0)
                
                sync_data["sync_count"] = current_count + 1
                
                supabase.table("vinted_sync_status").update(sync_data).eq("vinted_user_id", vinted_user_id).eq("user_id", user_id).execute()
            else:
                # Insert new record
                sync_data.update({
                    "vinted_user_id": vinted_user_id,
                    "user_id": user_id,
                    "sync_count": 1,
                    "created_at": datetime.now(timezone.utc).isoformat()
                })
                supabase.table("vinted_sync_status").insert(sync_data).execute()
                
            logger.info(f"✅ Updated sync status with last notification ID: {newest_notification_id}")
            
        except Exception as e:
            logger.error(f"❌ Error updating sync status: {e}")
    
    logger.info(f"💾 Saved {saved_count} new data entries to database")
    
    # Calculate stats
    likes_saved = len([d for d in all_new_data if d.get("data_type") == "like"])
    follows_saved = len([d for d in all_new_data if d.get("data_type") == "follow"])
    
    return {
        "total_notifications_processed": len(all_new_data),
        "data_saved": saved_count,
        "likes_found": likes_saved,
        "follows_found": follows_saved,
        "pages_processed": page - 1,
        "last_notification_id": newest_notification_id,
        "status": "success"
    }





# --- NEW WORKFLOW: GET UNREAD MESSAGES ---
async def workflow_get_unread_messages(user_id: str, params: dict) -> dict:
    """
    Fetches unread conversations from the user's Vinted inbox.
    Similar to the offers workflow but filters for unread messages only.
    """
    logger.info(f"💬 Starting workflow_get_unread_messages for user {user_id}")
    
    domain = get_active_domain(user_id, params)
    logger.info(f"🌐 Using domain: {domain}")
    
    # Check if user is logged in
    vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
    if not (vinted_user_info and vinted_user_info.get("id")):
        try:
            login_result = await workflow_check_vinted_login(user_id, params)
            if not login_result.get("is_logged_in"):
                return {"unread_messages": [], "error": "User not logged into Vinted. Please log in first."}
            vinted_user_info = manager.get_extension_data(user_id, "vinted_user_info")
            if not (vinted_user_info and vinted_user_info.get("id")):
                return {"unread_messages": [], "error": "Vinted user information not available after login check."}
        except Exception as e:
            return {"unread_messages": [], "error": f"Unable to verify login status: {str(e)}"}
    
    all_unread_messages = []
    page = 1
    MAX_PAGES = 5  # Reasonable limit for inbox scanning
    
    # Stage 1: Fetch conversations from inbox, filtering for unread only
    logger.info("📥 Stage 1: Fetching conversations from inbox...")
    while page <= MAX_PAGES:
        inbox_url = f"https://{domain}/api/v2/inbox?page={page}&per_page=50"
        logger.info(f"📋 Fetching inbox page {page}: {inbox_url}")
        
        try:
            resp = await perform_fetch_via_extension(
                user_id, {"url": inbox_url, "method": "GET"}
            )
            logger.info(f"📊 Inbox page {page} response status: {resp.get('http_status_code', 'UNKNOWN')}")
            
            if resp.get("http_status_code", 500) != 200:
                logger.warning(f"❌ Failed to fetch inbox page {page}: HTTP {resp.get('http_status_code')}")
                break
            
            data = json.loads(resp.get("raw_response_body", "{}"))
            conversations_on_page = data.get("conversations", [])
            logger.info(f"💬 Found {len(conversations_on_page)} conversations on page {page}")
            
            if not conversations_on_page:
                logger.info(f"📝 No more conversations found on page {page}, stopping")
                break
            
            # Filter for unread conversations directly from the inbox response
            unread_conversations = []
            for conv in conversations_on_page:
                if conv.get("unread") is True:  # Only conversations with unread: true
                    try:
                        # Extract time information
                        updated_at = conv.get("updated_at", "")
                        time_ago = ""
                        if updated_at:
                            try:
                                # Parse ISO datetime like "2025-08-05T18:08:41+02:00"
                                conv_datetime = datetime.fromisoformat(updated_at.replace("Z", "+00:00"))
                                time_ago = format_time_ago(int(conv_datetime.timestamp()))
                            except:
                                time_ago = "Unknown time"
                        
                        # Extract user information
                        opposite_user = conv.get("opposite_user", {})
                        user_photo = opposite_user.get("photo", {})
                        user_avatar_url = user_photo.get("url") if user_photo else None
                        
                        # Extract item photo information
                        item_photos = conv.get("item_photos", [])
                        item_photo_url = None
                        if item_photos and len(item_photos) > 0:
                            main_photo = item_photos[0]
                            item_photo_url = main_photo.get("url")
                        
                        processed_message = {
                            "conversation_id": conv.get("id"),
                            "description": conv.get("description", ""),
                            "unread": conv.get("unread", False),
                            "updated_at": updated_at,
                            "time_ago": time_ago,
                            "item_count": conv.get("item_count", 1),
                            "opposite_user": {
                                "id": opposite_user.get("id"),
                                "username": opposite_user.get("login", "unknown"),
                                "avatar_url": user_avatar_url,
                                "badge": opposite_user.get("badge")
                            },
                            "item_photo_url": item_photo_url,
                            "domain": domain,  # Add domain for frontend URL construction
                            "chat_url": f"https://{domain}/conversations/{conv.get('id')}"  # Direct chat URL
                        }
                        
                        unread_conversations.append(processed_message)
                        logger.debug(f"💬 Added unread conversation {conv.get('id')} from {opposite_user.get('login', 'unknown')}")
                        
                    except Exception as e:
                        logger.warning(f"⚠️ Error processing conversation {conv.get('id', 'unknown')}: {e}")
                        continue
            
            all_unread_messages.extend(unread_conversations)
            logger.info(f"💬 Found {len(unread_conversations)} unread conversations on page {page}")
            
            # Check pagination
            pagination = data.get("pagination", {})
            current_page = pagination.get("current_page", page)
            total_pages = pagination.get("total_pages", 1)
            logger.info(f"📄 Pagination: {current_page}/{total_pages}")
            
            if current_page >= total_pages:
                logger.info("✅ Reached last page")
                break
            
            page += 1
            await asyncio.sleep(0.5)  # Rate limiting
            
        except Exception as e:
            logger.error(f"❌ Error fetching inbox page {page} for user {user_id}: {e}")
            break
    
    logger.info(f"💬 Total unread messages found: {len(all_unread_messages)} across {page - 1} pages")
    
    # DEBUG: Log first few messages for verification
    if all_unread_messages:
        logger.info(f"🔍 Sample unread message structure: {all_unread_messages[0]}")
    
    # Sort by updated_at descending (most recent first)
    try:
        all_unread_messages.sort(
            key=lambda x: datetime.fromisoformat(x["updated_at"].replace("Z", "+00:00")),
            reverse=True
        )
    except:
        # If sorting fails, keep original order
        pass
    
    # 🚀 SEND PROGRESSIVE UPDATE TO FRONTEND (Final page)
    try:
        await manager.send_to_user_react(
            user_id,
            {
                "type": "UNREAD_MESSAGES_PAGE",
                "workflow_key": "GET_UNREAD_MESSAGES",
                "page_number": 1,
                "messages": all_unread_messages,
                "pagination": {
                    "current_page": 1,
                    "total_pages": 1,
                    "is_last_page": True,
                    "total_loaded": len(all_unread_messages)
                }
            }
        )
        logger.info(f"📤 Sent final page with {len(all_unread_messages)} messages to frontend")
    except Exception as e:
        logger.error(f"❌ Failed to send messages page to frontend: {e}")

    # Still return the traditional result for backward compatibility
    result = {
        "unread_messages": all_unread_messages,
        "total_count": len(all_unread_messages),
        "pages_scanned": page - 1
    }
    
    logger.info(f"💬 Returning result with {len(all_unread_messages)} unread messages")
    return result


# --- NEW BACKGROUND TASK WORKFLOWS ---
async def workflow_get_background_tasks(user_id: str, params: dict) -> dict:
    """
    Gets all background tasks for a user.
    """
    from app.services.background_tasks import background_task_manager
    tasks = await background_task_manager.get_user_tasks(user_id)
    return {"tasks": tasks}


async def workflow_get_background_task_status(user_id: str, params: dict) -> dict:
    """
    Gets status of a specific background task.
    """
    from app.services.background_tasks import background_task_manager
    task_id = params.get("task_id")
    if not task_id:
        raise ValueError("Task ID is required")
    
    status = await background_task_manager.get_task_status(task_id)
    if not status:
        return {"error": "Task not found"}
    
    return {"task": status}


async def workflow_cancel_background_task(user_id: str, params: dict) -> dict:
    """
    Cancels a running background task.
    """
    from app.services.background_tasks import background_task_manager
    task_id = params.get("task_id")
    if not task_id:
        raise ValueError("Task ID is required")
    
    cancelled = await background_task_manager.cancel_task(task_id)
    if cancelled:
        return {"status": "cancelled", "message": f"Task {task_id} cancelled successfully"}
    else:
        return {"status": "not_found", "message": f"Task {task_id} not found or already completed"}


async def workflow_get_background_task_stats(user_id: str, params: dict) -> dict:
    """
    Gets statistics about background tasks (for debugging/monitoring).
    """
    from app.services.background_tasks import background_task_manager
    stats = background_task_manager.get_stats()
    return {"stats": stats}


def vinted_order_status_mapper(vinted_status: str) -> str:
    """
    Maps Vinted's transaction_user_status to our frontend status.
    """
    status_mapping = {
        "completed": "completed",
        "failed": "cancelled", 
        "cancelled": "cancelled",
        "pending": "pending",
        "shipped": "shipped",
        "processing": "pending"
    }
    return status_mapping.get(vinted_status, "pending")


# --- Workflow Map ---
WORKFLOW_MAP: Dict[str, Coroutine] = {
    # --- PRODOTTI E GESTIONE INVENTARIO ---
    "GET_USER_PRODUCTS": workflow_get_user_products,
    "REPOST_ITEM": workflow_repost_item,
    "DELETE_ITEM": workflow_delete_item,
    
    # --- RICERCA E NAVIGAZIONE ---

    
    # --- AUTENTICAZIONE E STATO ---
    "CHECK_VINTED_LOGIN_STATUS": workflow_check_vinted_login,
    "GET_VINTED_STATS": workflow_vinted_stats,
    "GET_USER_PROFILE": workflow_get_user_profile,
    
    # --- OFFERTE E NEGOZIAZIONI ---
    "GET_VINTED_OFFERS": workflow_get_vinted_offers,
    "ACCEPT_VINTED_OFFER": workflow_accept_offer,
    "DECLINE_VINTED_OFFER": workflow_decline_offer,
    "COUNTER_VINTED_OFFER": workflow_counter_offer,
    
    # --- ORDINI E MESSAGGI ---
    "GET_VINTED_ORDERS": workflow_get_vinted_orders,
    "GET_UNREAD_MESSAGES": workflow_get_unread_messages,
    
    # --- FOLLOW E AUTOMAZIONE ---
    "FOLLOW_USER": workflow_follow_unified,
    "GET_FOLLOWER_STATS": workflow_get_follower_stats,
    
    # --- AUTO MESSAGGI ---
    "GET_AUTO_MESSAGE_SETTINGS": workflow_get_auto_message_settings,
    "TOGGLE_AUTO_MESSAGES": workflow_toggle_auto_messages,
    "CREATE_AUTO_MESSAGE": workflow_create_auto_message,
    "UPDATE_AUTO_MESSAGE": workflow_update_auto_message,
    "DELETE_AUTO_MESSAGE": workflow_delete_auto_message,
    "SEND_MANUAL_MESSAGE": workflow_send_manual_message,
    "GET_AUTO_MESSAGE_USAGE": workflow_get_auto_message_usage,
    
    # --- PIANI E UTILIZZO ---
    "GET_PLAN_AND_USAGE": workflow_get_plan_and_usage,
    
    # --- LIKES E NOTIFICHE ---
    "GET_RECENT_LIKES": workflow_get_recent_likes_with_sync,
    "SYNC_VINTED_NOTIFICATIONS": workflow_sync_vinted_notifications,
    
    # --- BACKGROUND TASKS ---
    "GET_BACKGROUND_TASKS": workflow_get_background_tasks,
    "GET_BACKGROUND_TASK_STATUS": workflow_get_background_task_status,
    "CANCEL_BACKGROUND_TASK": workflow_cancel_background_task,
    "GET_BACKGROUND_TASK_STATS": workflow_get_background_task_stats,
}