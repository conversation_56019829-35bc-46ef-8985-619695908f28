import { useEffect, useState } from "react";
import { useAuth } from "@/context/AuthContext";
import { useWebSocket } from "@/context/WebSocketContext";
import { supabase } from "@/integrations/supabase/client";

// Extend Window interface to include chrome extension APIs
declare global {
  interface Window {
    chrome?: {
      runtime?: {
        sendMessage: (extensionId: string, message: any) => Promise<any>;
      };
    };
  }
}

export const useExtensionAuth = () => {
  const { session } = useAuth();
  const { isExtensionOnline } = useWebSocket();
  const [isExtensionConnected, setIsExtensionConnected] = useState(false);
  const [authSent, setAuthSent] = useState(false);
  const [authAckReceived, setAuthAckReceived] = useState(false);
  const [extensionId, setExtensionId] = useState<string | null>(null);
  const [shouldRedirect, setShouldRedirect] = useState(false);

  // Check if this is an extension request
  const urlParams = new URLSearchParams(window.location.search);
  const isExtensionRequest = urlParams.get("extension") === "true";
  
  // Debug log for extension request detection
  useEffect(() => {
    if (isExtensionRequest) {
      console.log("🔐 [ExtensionAuth] Extension request detected:", {
        url: window.location.href,
        extensionParam: urlParams.get("extension"),
        isExtensionRequest
      });
    }
  }, [isExtensionRequest]);

  // Get extension ID from injected meta tag
  useEffect(() => {
    if (!isExtensionRequest) return;

    const checkExtensionId = () => {
      const metaTag = document.querySelector('meta[name="vindy-extension-id"]');
      if (metaTag) {
        const id = metaTag.getAttribute("content");
        if (id) {
          setExtensionId(id);
          setIsExtensionConnected(true);
          console.log("🔐 [ExtensionAuth] Vindy Extension ID found:", id);
          return true;
        }
      }
      console.log("🔐 [ExtensionAuth] Extension ID not found yet, meta tag:", metaTag);
      return false;
    };

    // Check immediately
    if (!checkExtensionId()) {
      // If not found, poll for a short time
      // Polling removed - check immediately
      checkExtensionId();
    }
  }, [isExtensionRequest]);

  // Check if extension is already connected via WebSocket when page loads
  useEffect(() => {
    if (!isExtensionRequest || !session) return;

    // If extension is already online via WebSocket, redirect immediately
    if (isExtensionOnline === true) {
      console.log(
        "Extension already connected via WebSocket, redirecting to dashboard"
      );
      setShouldRedirect(true);
    }
  }, [isExtensionRequest, session, isExtensionOnline]);

  // Redirect to dashboard when extension connects via WebSocket after auth
  useEffect(() => {
    if (!isExtensionRequest) return;

    // If we sent auth and extension comes online, redirect
    if (authSent && isExtensionOnline === true) {
      console.log(
        "Extension connected via WebSocket after auth, redirecting to dashboard"
      );
      setShouldRedirect(true);
    }
  }, [isExtensionRequest, authSent, isExtensionOnline]);

  // Also listen for in-page explicit online event and redirect if auth was already sent
  useEffect(() => {
    if (!isExtensionRequest) return;
    const handler = () => {
      if (authSent) {
        console.log('🔐 [ExtensionAuth] Received VINDY_EXTENSION_ONLINE and auth already sent -> redirecting');
        setShouldRedirect(true);
      }
    };

    window.addEventListener('VINDY_EXTENSION_ONLINE', handler as EventListener);
    return () => window.removeEventListener('VINDY_EXTENSION_ONLINE', handler as EventListener);
  }, [isExtensionRequest, authSent]);

  // Fallback: listen directly for content-script presence messages and redirect if authSent
  useEffect(() => {
    if (!isExtensionRequest) return;
    const onPresence = (event: MessageEvent) => {
      const data: any = event?.data;
      if (!data || typeof data !== 'object') return;
      const t = data.type as string | undefined;
      if (!t) return;
      if (
        t === 'VINDY_PRESENCE_PONG' ||
        t === 'VINDY_EXTENSION_PONG' ||
        t === 'VINDY_EXTENSION_ALIVE' ||
        t === 'VINDY_EXTENSION_ALIVE_DIRECT'
      ) {
        if (authSent) {
          console.log('🔐 [ExtensionAuth] Presence message received and auth already sent -> redirecting');
          setShouldRedirect(true);
        }
      }
    };
    window.addEventListener('message', onPresence);
    return () => window.removeEventListener('message', onPresence);
  }, [isExtensionRequest, authSent]);

  // Handle redirect
  useEffect(() => {
    if (shouldRedirect) {
      // Small delay to show success message before redirect
      // Immediate redirect - no timeout needed
      window.location.href = "/";
    }
  }, [shouldRedirect]);

  const sendAuthStateToExtension = async (
    event: "SIGNED_IN" | "SIGNED_OUT",
    sessionData?: any
  ) => {
  // Note: extensionId is required only for chrome.runtime path; postMessage fallback doesn't require it

    try {
      const message = {
        type: "AUTH_STATE_UPDATE_FROM_PAGE",
        payload: {
          event,
          session: sessionData || null,
        },
      };

      console.log("🔐 [ExtensionAuth] Sending auth state via chrome.runtime", {
        extensionId,
        event,
        hasSession: !!sessionData,
      });

      // Prefer chrome.runtime when available; otherwise fallback to window.postMessage
      if (extensionId && window.chrome && window.chrome.runtime) {
        try {
          // Expect extension to reply with an explicit ack: { success: true, ack: true }
          const response = await window.chrome.runtime.sendMessage(extensionId, message);
          console.log("🔐 [ExtensionAuth] Auth state sent via chrome.runtime, response:", response);
          if (response && (response.ack === true || response.success === true)) {
            // mark ack only when extension explicitly acknowledges
            setAuthAckReceived(true);
            // Notify page-level listeners (WebSocketContext) that extension acked auth
            try {
              window.dispatchEvent(new CustomEvent('VINDY_EXTENSION_AUTH_ACK', { detail: { extensionId } }));
            } catch {}
          }
        } catch (e) {
          console.error("🔐 [ExtensionAuth] chrome.runtime.sendMessage failed:", e);
          return false;
        }
      } else {
        console.warn("🔐 [ExtensionAuth] chrome.runtime not available; falling back to window.postMessage");
        window.postMessage({
          type: "AUTH_STATE_UPDATE_FROM_PAGE",
          payload: {
            event,
            session: sessionData || null,
          },
          target: "VINDY_EXTENSION",
        }, "*");
      }

      if (event === "SIGNED_IN") {
        // For safety, consider authSent true only when ack received OR fallback path used
        // If chrome.runtime path provided an ack we already set authAckReceived
        if (authAckReceived) {
          setAuthSent(true);
        } else {
          // If runtime isn't available or ack didn't arrive, fall back to marking authSent
          // after a small delay to allow extension to respond and attach backend connection.
          setTimeout(() => {
            setAuthSent(true);
            try {
              window.dispatchEvent(new CustomEvent('VINDY_EXTENSION_AUTH_SENT', { detail: { extensionId } }));
            } catch {}
          }, 250);
        }
      }
      return true;
    } catch (error) {
      console.error("🔐 [ExtensionAuth] Error sending auth state to extension:", error);
      return false;
    }
  };

  useEffect(() => {
    // Only proceed if this is an extension request
    if (!isExtensionRequest) {
      return;
    }

    // If extension is already online via WebSocket, no need to send auth
    if (isExtensionOnline === true && session) {
      console.log(
        "Extension already connected via WebSocket, no need to send auth messages"
      );
      setIsExtensionConnected(true);
      setAuthSent(true);
      return;
    }

    // If extension is offline or unknown and we have extension ID, send auth
    if (
      (isExtensionOnline === false || isExtensionOnline === null) &&
      extensionId &&
      session &&
      !authSent
    ) {
      console.log(
        "Extension ID available and user authenticated, sending auth state"
      );
      sendAuthStateToExtension("SIGNED_IN", session);
    }
  }, [session, authSent, isExtensionRequest, isExtensionOnline, extensionId]);

  // Listen for auth state changes only if extension is not already online
  useEffect(() => {
    if (!isExtensionRequest) {
      return;
    }

    // If extension is already online, don't listen for auth changes
    if (isExtensionOnline === true) {
      return;
    }

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, currentSession) => {
      console.log("Auth state changed for extension:", event);

      if (
        event === "SIGNED_IN" &&
        currentSession &&
        extensionId &&
          !isExtensionOnline
      ) {
        sendAuthStateToExtension("SIGNED_IN", currentSession);
      } else if (event === "SIGNED_OUT" && extensionId) {
        sendAuthStateToExtension("SIGNED_OUT");
        setAuthSent(false);
      }
    });

    return () => subscription.unsubscribe();
  }, [extensionId, isExtensionRequest, isExtensionOnline]);

  return {
    isExtensionConnected: isExtensionRequest
      ? isExtensionOnline === true
        ? true
        : isExtensionConnected && !!extensionId
      : false,
    authSent: isExtensionRequest
      ? isExtensionOnline === true
        ? true
        : authSent
      : false,
    sendAuthStateToExtension,
    setAuthSent,
    extensionId,
    shouldRedirect,
  };
};
