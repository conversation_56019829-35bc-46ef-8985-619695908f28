# app/services/job_monitor.py
import asyncio
import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta

from app.services.job_controller import job_controller
from app.services.connection_manager import manager

logger = logging.getLogger(__name__)


class JobMonitor:
    """
    Sistema di monitoraggio per i job persistenti.
    Controlla la salute dei job e riavvia quelli bloccati.
    """
    
    def __init__(self):
        self.monitoring_task: Optional[asyncio.Task] = None
        self.is_running = False
        self.health_check_interval = 300  # 5 minuti
        self.max_inactivity_minutes = 30  # 30 minuti di inattività massima
        self.cleanup_interval = 3600  # 1 ora per cleanup
        
    async def start_monitoring(self):
        """Avvia il monitoraggio dei job persistenti"""
        if self.is_running:
            logger.warning("Job monitor is already running")
            return
        
        self.is_running = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("🔍 Job monitor started")
    
    async def stop_monitoring(self):
        """Ferma il monitoraggio dei job persistenti"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.monitoring_task and not self.monitoring_task.done():
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("🔍 Job monitor stopped")
    
    async def _monitoring_loop(self):
        """Loop principale del monitoraggio"""
        while self.is_running:
            try:
                # Controlla salute dei job
                await self._check_job_health()
                
                # Pulisce log vecchi
                await self._cleanup_old_logs()
                
                # Attendi prossimo controllo
                await asyncio.sleep(self.health_check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in job monitoring loop: {e}")
                await asyncio.sleep(60)  # Attendi 1 minuto prima di riprovare
    
    async def _check_job_health(self):
        """Controlla la salute di tutti i job attivi"""
        try:
            stats = job_controller.get_stats()
            total_jobs = stats.get("total_jobs", 0)
            
            if total_jobs == 0:
                return
            
            logger.debug(f"🔍 Checking health of {total_jobs} active jobs")
            
            # Controlla ogni job per inattività
            for job_id, job_state in job_controller.job_states.items():
                if not self.is_running:
                    break
                
                await self._check_single_job_health(job_id, job_state)
                
        except Exception as e:
            logger.error(f"Error checking job health: {e}")
    
    async def _check_single_job_health(self, job_id: str, job_state: Dict[str, Any]):
        """Controlla la salute di un singolo job"""
        try:
            # Controlla se il job è inattivo da troppo tempo
            last_activity = job_state.get("last_activity", 0)
            current_time = time.time()
            inactivity_minutes = (current_time - last_activity) / 60
            
            if inactivity_minutes > self.max_inactivity_minutes:
                logger.warning(f"⚠️ Job {job_id} inactive for {inactivity_minutes:.1f} minutes")
                
                # Se il job è in esecuzione ma inattivo, riavvialo
                if job_state.get("status") == "running":
                    await self._restart_stuck_job(job_id, job_state)
            
            # Controlla se il job ha troppi errori consecutivi
            error_count = job_state.get("error_count", 0)
            if error_count >= 10:
                logger.warning(f"⚠️ Job {job_id} has {error_count} consecutive errors")
                
                # Metti in pausa il job con troppi errori
                if job_state.get("status") == "running":
                    await job_controller.pause_job(
                        job_id, 
                        f"Auto-paused due to {error_count} consecutive errors"
                    )
                    
        except Exception as e:
            logger.error(f"Error checking health of job {job_id}: {e}")
    
    async def _restart_stuck_job(self, job_id: str, job_state: Dict[str, Any]):
        """Riavvia un job bloccato"""
        try:
            user_id = job_state["user_id"]
            job_type = job_state["job_type"]
            vinted_user_id = job_state["vinted_user_id"]
            config = job_state.get("config", {})
            
            logger.info(f"🔄 Restarting stuck job {job_id} ({job_type})")
            
            # Ferma il job corrente
            await job_controller.stop_job(job_id)
            
            # Avvia un nuovo job dello stesso tipo
            new_job_id = await job_controller.start_persistent_job(
                user_id, job_type, vinted_user_id, config
            )
            
            if new_job_id:
                logger.info(f"✅ Successfully restarted job {job_id} -> {new_job_id}")
                
                # Notifica l'utente
                await manager.send_to_user_react(user_id, {
                    "type": "JOB_RESTARTED",
                    "old_job_id": job_id,
                    "new_job_id": new_job_id,
                    "job_type": job_type,
                    "reason": "Job was stuck and has been automatically restarted"
                })
            else:
                logger.error(f"❌ Failed to restart job {job_id}")
                
        except Exception as e:
            logger.error(f"Error restarting stuck job {job_id}: {e}")
    
    async def _cleanup_old_logs(self):
        """Pulisce i log vecchi per mantenere le performance"""
        try:
            # Questa funzione potrebbe essere chiamata periodicamente
            # per pulire i log vecchi dal database
            # Per ora è un placeholder
            pass
            
        except Exception as e:
            logger.error(f"Error cleaning up old logs: {e}")
    
    async def get_health_report(self) -> Dict[str, Any]:
        """Genera un report sulla salute dei job"""
        try:
            stats = job_controller.get_stats()
            
            # Analizza job problematici
            problematic_jobs = []
            for job_id, job_state in job_controller.job_states.items():
                last_activity = job_state.get("last_activity", 0)
                current_time = time.time()
                inactivity_minutes = (current_time - last_activity) / 60
                error_count = job_state.get("error_count", 0)
                
                if inactivity_minutes > self.max_inactivity_minutes or error_count >= 5:
                    problematic_jobs.append({
                        "job_id": job_id,
                        "job_type": job_state.get("job_type"),
                        "user_id": job_state.get("user_id"),
                        "status": job_state.get("status"),
                        "inactivity_minutes": inactivity_minutes,
                        "error_count": error_count,
                        "last_activity": datetime.fromtimestamp(last_activity).isoformat()
                    })
            
            return {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "total_jobs": stats.get("total_jobs", 0),
                "running_jobs": stats.get("running_jobs", 0),
                "paused_jobs": stats.get("paused_jobs", 0),
                "problematic_jobs": problematic_jobs,
                "problematic_count": len(problematic_jobs),
                "monitor_status": "running" if self.is_running else "stopped"
            }
            
        except Exception as e:
            logger.error(f"Error generating health report: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }


# Istanza globale del monitor
job_monitor = JobMonitor()
