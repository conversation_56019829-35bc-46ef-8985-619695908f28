# app/api/v1/endpoints/likes.py
from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any
import logging
from app.db.supabase_client import supabase
from app.core.security import get_current_user_id
from app.services.likes_workflows import (
    workflow_sync_vinted_likes, 
    workflow_get_recent_likes_with_sync
)

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/sync")
async def sync_vinted_likes(current_user_id: str = Depends(get_current_user_id)):
    """
    Sync only LIKES from Vinted notifications to extract and save like data.
    This endpoint fetches notifications but only processes and saves likes.
    """
    try:
        logger.info(f"Starting likes sync for user {current_user_id}")
        
        # Call the workflow to sync likes only
        result = await workflow_sync_vinted_likes(current_user_id, {})
        
        return {
            "status": "success",
            "message": f"Synced {result.get('likes_saved', 0)} new likes",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Error syncing likes for user {current_user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to sync likes: {str(e)}")


@router.get("/recent")
async def get_recent_likes(current_user_id: str = Depends(get_current_user_id)):
    """
    Get recent likes for the dashboard Recent Likes card.
    """
    try:
        logger.info(f"Getting recent likes for user {current_user_id}")
        
        # Call the workflow to get recent likes with sync
        result = await workflow_get_recent_likes_with_sync(current_user_id, {})
        
        return {
            "status": "success",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Error getting recent likes for user {current_user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get recent likes: {str(e)}")


@router.get("/item/{item_id}")
async def get_item_likes(item_id: str, current_user_id: str = Depends(get_current_user_id)):
    """
    Get all likes for a specific item.
    """
    try:
        logger.info(f"Getting likes for item {item_id} for user {current_user_id}")
        
        # Use the unified workflow with item_id parameter
        result = await workflow_get_recent_likes_with_sync(current_user_id, {"item_id": item_id})
        
        return {
            "status": "success",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Error getting likes for item {item_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get item likes: {str(e)}")


@router.get("/stats")
async def get_likes_stats(current_user_id: str = Depends(get_current_user_id)):
    """
    Get overall likes statistics for the user.
    """
    try:
        logger.info(f"Getting likes stats for user {current_user_id}")
        
        # Use the unified workflow to get stats
        result = await workflow_get_recent_likes_with_sync(current_user_id, {"include_stats": True})
        
        return {
            "status": "success",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Error getting likes stats for user {current_user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get likes stats: {str(e)}")


@router.delete("/item/{item_id}")
async def delete_item_likes(item_id: str, current_user_id: str = Depends(get_current_user_id)):
    """
    Delete all likes for a specific item (when item is deleted).
    """
    try:
        logger.info(f"Deleting likes for item {item_id} for user {current_user_id}")
        
        # Get Vinted user ID first
        from app.services.connection_manager import manager
        vinted_user_info = manager.get_extension_data(current_user_id, "vinted_user_info")
        if not (vinted_user_info and vinted_user_info.get("id")):
            raise HTTPException(status_code=400, detail="Vinted user information not available")
        
        vinted_user_id = str(vinted_user_info["id"])
        
        # Delete all likes for this item
        delete_response = (
            supabase.table("vinted_likes")
            .delete()
            .eq("vinted_user_id", vinted_user_id)
            .eq("user_id", current_user_id)
            .eq("item_id", str(item_id))
            .execute()
        )
        
        deleted_count = len(delete_response.data) if delete_response.data else 0
        
        return {
            "status": "success",
            "message": f"Deleted {deleted_count} likes for item {item_id}",
            "deleted_count": deleted_count
        }
        
    except Exception as e:
        logger.error(f"Error deleting likes for item {item_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete item likes: {str(e)}")


@router.get("/sync-status")
async def get_sync_status(current_user_id: str = Depends(get_current_user_id)):
    """
    Get the current sync status for the user based on existing likes.
    """
    try:
        logger.info(f"Getting sync status for user {current_user_id}")
        
        # Get Vinted user ID first
        from app.services.connection_manager import manager
        vinted_user_info = manager.get_extension_data(current_user_id, "vinted_user_info")
        if not (vinted_user_info and vinted_user_info.get("id")):
            raise HTTPException(status_code=400, detail="Vinted user information not available")
        
        vinted_user_id = str(vinted_user_info["id"])
        
        # Get total likes count and last sync info
        likes_response = (
            supabase.table("vinted_likes")
            .select("notification_id, liked_at", count="exact")
            .eq("vinted_user_id", vinted_user_id)
            .eq("user_id", current_user_id)
            .order("liked_at", desc=True)
            .limit(1)
            .execute()
        )
        
        total_likes = likes_response.count or 0
        last_like = likes_response.data[0] if likes_response.data else None
        
        return {
            "status": "success",
            "data": {
                "has_synced": total_likes > 0,
                "last_sync_at": last_like.get("liked_at") if last_like else None,
                "likes_synced_count": total_likes,
                "last_notification_id": last_like.get("notification_id") if last_like else None
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting sync status for user {current_user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get sync status: {str(e)}")
