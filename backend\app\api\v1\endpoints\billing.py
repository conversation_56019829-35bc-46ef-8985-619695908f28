# app/api/v1/endpoints/billing.py
import stripe
from fastapi import APIRouter, Request, Header, Depends, HTTPException, status
from typing import Dict

from app.core.config import settings, logger, STRIPE_PRODUCT_TO_PLAN
from app.db.supabase_client import supabase
from app.models.plans import PLAN_KEYWORD_MAP

# A dependency to get user ID from an Authorization header for HTTP routes
async def get_current_user_id(request: Request) -> str:
    # Assuming the JWT is passed as 'Bearer <token>'
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated"
        )

    token = auth_header.split(" ")[1]
    # This is a simplified version. Ideally you'd use your full get_user_id_from_token function
    from app.core.security import get_user_id_from_token

    user_id = await get_user_id_from_token(token)

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token"
        )
    return user_id


router = APIRouter()


@router.post("/create-checkout-session")
async def create_checkout_session(
    data: Dict, user_id: str = Depends(get_current_user_id)
):
    """
    Creates a Stripe Checkout session for a user to purchase a subscription.
    The frontend sends a human-readable plan_key (e.g., "essentials").
    """
    # 1. The frontend now sends a simple, abstract keyword.
    plan_key = data.get("plan_key")
    if not plan_key:
        raise HTTPException(status_code=400, detail="Plan key is required.")

    # 2. Look up the plan details in our secure, keyword-based map.
    plan_details = PLAN_KEYWORD_MAP.get(plan_key)
    if not plan_details:
        raise HTTPException(status_code=404, detail="Invalid plan key.")

    # 3. Determine price strictly from environment configuration by key
    from app.core.config import settings as app_settings
    is_live_key = app_settings.STRIPE_API_KEY.startswith("sk_live")
    env_price_map = {
        "essentials": app_settings.STRIPE_PRICE_ESSENTIALS,
        "essentials_plus": app_settings.STRIPE_PRICE_ESSENTIALS_PLUS,
    }
    price_id = env_price_map.get(plan_key)
    if not price_id or "REPLACE_ME" in str(price_id):
        mode = "live" if is_live_key else "test"
        raise HTTPException(
            status_code=400,
            detail=(
                f"Missing or invalid Stripe price ID for plan '{plan_key}'. "
                f"Set STRIPE_PRICE_ESSENTIALS/STRIPE_PRICE_ESSENTIALS_PLUS for {mode} mode."
            ),
        )

    logger.info(
        f"Creating Stripe checkout for user {user_id} | plan_key={plan_key} price_id={price_id}"
    )

    try:
    # Validate that the price exists in the current Stripe mode (test/live)
        try:
            _ = stripe.Price.retrieve(price_id)
        except stripe.error.InvalidRequestError as e:
            # Likely using the wrong API key mode for the configured price_id
            logger.error(
                f"Stripe price '{price_id}' not found under current API key for user {user_id}: {e}"
            )
            raise HTTPException(
                status_code=400,
                detail=(
                    "Stripe price not found. Check if backend STRIPE_API_KEY mode matches your configured price IDs (test vs live)."
                ),
            )

        # The rest of the function logic is exactly the same as before.
        # Allow zero-row result without raising 406 from PostgREST. Be defensive about DB errors.
        customer_id = None
        try:
            user_profile = (
                supabase.table("profiles")
                .select("stripe_customer_id")
                .eq("id", user_id)
                .maybe_single()
                .execute()
            )
            if user_profile is None:
                logger.debug(f"Supabase maybe_single returned None for profiles id={user_id}")
                user_profile_data = {}
            else:
                user_profile_data = getattr(user_profile, "data", None) or user_profile
            customer_id = (user_profile_data or {}).get("stripe_customer_id")
        except Exception as db_err:
            # Log and continue: the DB might be missing billing columns or have temporary errors
            logger.error(f"Stripe checkout session DB read error for user {user_id}: {db_err}")

        if not customer_id:
            customer = stripe.Customer.create(metadata={"user_id": user_id})
            customer_id = customer.id
            # Attempt to persist stripe_customer_id but don't fail the checkout flow if it errors
            try:
                supabase.table("profiles").upsert({
                    "id": user_id,
                    "stripe_customer_id": customer_id,
                }).execute()
            except Exception as db_err:
                logger.warning(f"Upsert profiles failed for {user_id}: {db_err}")

        checkout_session = stripe.checkout.Session.create(
            customer=customer_id,
            payment_method_types=["card"],
            line_items=[{"price": price_id, "quantity": 1}],
            mode="subscription",
            success_url=f"{settings.FRONTEND_URL}/payment-success",
            cancel_url=f"{settings.FRONTEND_URL}/payment-cancelled",
            client_reference_id=user_id,
        )
        return {"sessionId": checkout_session.id, "url": checkout_session.url}
    except stripe.error.InvalidRequestError as e:
        logger.error(f"Stripe InvalidRequestError for user {user_id}: {e}")
        raise HTTPException(status_code=400, detail=f"Stripe invalid request: {str(e)}")
    except stripe.error.AuthenticationError as e:
        logger.error(f"Stripe AuthenticationError for user {user_id}: {e}")
        detail_msg = (
            "Stripe authentication failed. Check STRIPE_API_KEY and ensure it matches your price IDs (test/live)."
        )
        raise HTTPException(status_code=500, detail=detail_msg)
    except stripe.error.APIConnectionError as e:
        logger.error(f"Stripe APIConnectionError for user {user_id}: {e}")
        raise HTTPException(status_code=502, detail="Network error contacting Stripe.")
    except Exception as e:
        logger.error(f"Stripe checkout session error for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Unexpected error creating Stripe session.")


@router.post("/create-portal-session")
async def create_portal_session(user_id: str = Depends(get_current_user_id)):
    """
    Creates a Stripe Customer Portal session for a user to manage their subscription.
    """
    try:
        # Portal: read from canonical profiles table (not legacy user_profiles)
        try:
            user_profile = (
                supabase.table("profiles")
                .select("stripe_customer_id")
                .eq("id", user_id)
                .maybe_single()
                .execute()
            )
            customer_id = (getattr(user_profile, "data", None) or user_profile or {}).get("stripe_customer_id")
        except Exception as db_err:
            logger.error(f"Stripe portal session DB read error for user {user_id}: {db_err}")
            customer_id = None

        if not customer_id:
            raise HTTPException(
                status_code=400, detail="User is not a Stripe customer."
            )

        portal_session = stripe.billing_portal.Session.create(
            customer=customer_id, return_url=f"{settings.FRONTEND_URL}/dashboard"
        )
        return {"url": portal_session.url}
    except Exception as e:
        logger.error(f"Stripe portal session error for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))



@router.post("/webhook")
async def stripe_webhook(request: Request, stripe_signature: str = Header(None, alias="Stripe-Signature")):
    """
    Listens for events from Stripe and updates the database accordingly.
    Handles new subscriptions, plan changes, and final cancellations.
    """
    payload = await request.body()
    try:
        event = stripe.Webhook.construct_event(
            payload=payload,
            sig_header=stripe_signature,
            secret=settings.STRIPE_WEBHOOK_SECRET,
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except stripe.error.SignatureVerificationError as e:
        raise HTTPException(status_code=400, detail=str(e))

    event_type = event["type"]
    data = event["data"]["object"]
    logger.info(f"Received Stripe webhook event: {event_type}")

    if event_type == "checkout.session.completed":
        # Handles new subscriptions
        user_id = data.get("client_reference_id")
        if not user_id:
            logger.error(
                "Webhook error: client_reference_id not found in checkout.session.completed"
            )
            return {"status": "error", "message": "User ID not found in session."}

        subscription = stripe.Subscription.retrieve(data.get("subscription"))
        # Prefer price.product from items for consistency with updated events
        try:
            product_id = subscription["items"]["data"][0]["price"]["product"]
        except Exception:
            # Fallback to legacy field if present
            product_id = getattr(getattr(subscription, "plan", None), "product", None)

        # Persist profile summary
        # Map Stripe product id to internal plan key if available
        product_to_plan = STRIPE_PRODUCT_TO_PLAN or {}
        plan_key = product_to_plan.get(product_id) if product_id else None
        supabase.table("profiles").upsert({
            "id": user_id,
            "plan_id": plan_key or product_id or "free",
            "subscription_status": subscription.status,
            "stripe_customer_id": data.get("customer"),
        }).execute()

        # Persist subscription record
        try:
            sub_record = {
                "subscription_id": subscription.id,
                "user_id": user_id,
                "stripe_customer_id": data.get("customer"),
                "price_id": subscription["items"]["data"][0]["price"]["id"] if subscription.get("items") else None,
                "product_id": product_id,
                "status": subscription.status,
                "current_period_end": subscription.get("current_period_end"),
            }
            supabase.table("user_subscriptions").upsert(sub_record).execute()
        except Exception as e:
            logger.warning(f"Failed to persist user_subscriptions for user {user_id}: {e}")

    elif event_type == "customer.subscription.updated":
        # Handles plan upgrades and downgrades.
        customer_id = data.get("customer")
        user_profile = (
            supabase.table("profiles")
            .select("id")
            .eq("stripe_customer_id", customer_id)
            .maybe_single()
            .execute()
        )

        if not user_profile or not getattr(user_profile, "data", None):
            logger.error(
                f"Webhook error: User not found for Stripe customer ID {customer_id}"
            )
            return {"status": "error", "message": "User not found for customer ID."}

        user_id = (user_profile.data or {}).get("id")
        subscription_data = data

        if subscription_data.get("items") and subscription_data["items"].get("data"):
            new_product_id = subscription_data["items"]["data"][0]["price"]["product"]
        else:
            logger.error(
                f"Webhook error: Could not find items in subscription object for user {user_id}"
            )
            return {
                "status": "error",
                "message": "Subscription items not found in webhook.",
            }

        product_to_plan = STRIPE_PRODUCT_TO_PLAN or {}
        new_plan_key = product_to_plan.get(new_product_id) if new_product_id else None
        supabase.table("profiles").upsert({
            "id": user_id,
            "plan_id": new_plan_key or new_product_id,
            "subscription_status": subscription_data.get("status"),
        }).execute()

        # Update subscription record in user_subscriptions
        try:
            sub_id = subscription_data.get("id") or subscription_data.get("subscription")
            supabase.table("user_subscriptions").upsert({
                "subscription_id": sub_id,
                "user_id": user_id,
                "price_id": subscription_data["items"]["data"][0]["price"]["id"],
                "product_id": new_product_id,
                "status": subscription_data.get("status"),
                "current_period_end": subscription_data.get("current_period_end"),
                "updated_at": "now()",
            }).execute()
        except Exception as e:
            logger.warning(f"Failed to update user_subscriptions for user {user_id}: {e}")

        logger.info(
            f"User {user_id} plan updated to {new_product_id} with status {subscription_data.get('status')}"
        )

    # --- NEW, SEPARATE LOGIC FOR FINAL CANCELLATION ---
    elif event_type == "customer.subscription.deleted":
        # Handles the event when a subscription is officially over.
        customer_id = data.get("customer")
        user_profile = (
            supabase.table("profiles")
            .select("id")
            .eq("stripe_customer_id", customer_id)
            .maybe_single()
            .execute()
        )

        if not user_profile or not getattr(user_profile, "data", None):
            logger.error(
                f"Webhook error: User not found for Stripe customer ID {customer_id} on subscription deletion."
            )
            return {"status": "error", "message": "User not found for customer ID."}

        user_id = (user_profile.data or {}).get("id")

        # Revert the user to the 'free' plan.
        supabase.table("profiles").upsert({
            "id": user_id,
            "plan_id": "free",
            "subscription_status": "canceled",
        }).execute()

        try:
            supabase.table("user_subscriptions").upsert({
                "subscription_id": data.get("id") or data.get("subscription"),
                "user_id": user_id,
                "status": "canceled",
                "updated_at": "now()",
            }).execute()
        except Exception as e:
            logger.warning(f"Failed to mark user_subscriptions canceled for user {user_id}: {e}")

        logger.info(f"User {user_id} subscription deleted. Reverted to 'free' plan.")

    # Additional important events to mark subscription/payment state
    elif event_type == "invoice.payment_failed":
        # The invoice contains subscription and customer references
        invoice = data
        customer_id = invoice.get("customer")
        sub_id = invoice.get("subscription")
        # Find user and mark subscription as past_due
        user_profile = (
            supabase.table("profiles").select("id").eq("stripe_customer_id", customer_id).maybe_single().execute()
        )
        user_id = (user_profile.data or {}).get("id") if user_profile else None
        if user_id:
            supabase.table("profiles").upsert({"id": user_id, "subscription_status": "past_due"}).execute()
            try:
                supabase.table("user_subscriptions").upsert({"subscription_id": sub_id, "user_id": user_id, "status": "past_due", "updated_at": "now()"}).execute()
            except Exception:
                pass
        logger.info(f"Invoice payment failed for customer {customer_id}, marked user {user_id} as past_due")

    elif event_type == "invoice.payment_succeeded":
        invoice = data
        customer_id = invoice.get("customer")
        sub_id = invoice.get("subscription")
        user_profile = (
            supabase.table("profiles").select("id").eq("stripe_customer_id", customer_id).maybe_single().execute()
        )
        user_id = (user_profile.data or {}).get("id") if user_profile else None
        if user_id:
            supabase.table("profiles").upsert({"id": user_id, "subscription_status": "active"}).execute()
            try:
                supabase.table("user_subscriptions").upsert({"subscription_id": sub_id, "user_id": user_id, "status": "active", "updated_at": "now()"}).execute()
            except Exception:
                pass
        logger.info(f"Invoice payment succeeded for customer {customer_id}, marked user {user_id} as active")

    elif event_type == "customer.subscription.trial_will_end":
        # Optional: notify user or mark trial ending
        subscription = data
        customer_id = subscription.get("customer")
        user_profile = (
            supabase.table("profiles").select("id").eq("stripe_customer_id", customer_id).maybe_single().execute()
        )
        user_id = (user_profile.data or {}).get("id") if user_profile else None
        logger.info(f"Subscription trial will end for user {user_id} (customer {customer_id})")

    else:
        logger.warning(f"Unhandled event type {event_type} received. No action taken.")

    return {"status": "success"}


@router.get("/me")
async def get_billing_me(user_id: str = Depends(get_current_user_id)):
    """Return the current user's plan and limits for frontend display/testing."""
    from app.models.plans import get_user_permissions
    try:
        perms = await get_user_permissions(user_id)
        # Attach stripe customer id if present for convenience
        profile = (
            supabase.table("profiles")
            .select("stripe_customer_id")
            .eq("id", user_id)
            .single()
            .execute()
        )
        return {
            "user_id": user_id,
            "stripe_customer_id": (profile.data or {}).get("stripe_customer_id") if profile else None,
            **perms,
        }
    except Exception as e:
        logger.error(f"Error getting billing info for {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch billing info")


@router.get("/diagnose")
async def diagnose_stripe():
    """Quick diagnostics for Stripe setup: key mode, account, and price visibility.
    Does NOT require auth so you can test setup easily.
    """
    from app.core.config import settings as app_settings
    mode = "live" if app_settings.STRIPE_API_KEY.startswith("sk_live") else "test"
    result: Dict[str, any] = {
        "mode": mode,
        "account": None,
        "api_key_ok": False,
        "prices": {},
        "notes": [
            "Ensure STRIPE_API_KEY and STRIPE_PRICE_* belong to the same Stripe account and mode.",
        ],
    }

    # 1) Check API key validity by retrieving the account
    try:
        acct = stripe.Account.retrieve()
        result["account"] = {
            "id": acct.get("id"),
            "email": acct.get("email"),
            "settings": {
                "dashboard": (acct.get("settings") or {}).get("dashboard", {}),
            },
        }
        result["api_key_ok"] = True
    except stripe.error.AuthenticationError as e:
        # Invalid API key
        result["error"] = "Invalid Stripe API key"
        result["stripe_message"] = str(e)
        return result
    except Exception as e:
        result["error"] = f"Unexpected error checking account: {e}"
        return result

    # 2) Check visibility of configured prices (if provided)
    price_map = {
        "essentials": app_settings.STRIPE_PRICE_ESSENTIALS,
        "essentials_plus": app_settings.STRIPE_PRICE_ESSENTIALS_PLUS,
    }
    for key, pid in price_map.items():
        if not pid:
            result["prices"][key] = {"configured": False, "visible": False, "message": "No env price configured"}
            continue
        try:
            pr = stripe.Price.retrieve(pid)
            result["prices"][key] = {
                "configured": True,
                "visible": True,
                "id": pr.get("id"),
                "active": pr.get("active"),
                "currency": pr.get("currency"),
                "product": pr.get("product"),
                "recurring": (pr.get("recurring") or {}),
            }
        except stripe.error.InvalidRequestError as e:
            result["prices"][key] = {
                "configured": True,
                "visible": False,
                "message": f"Price not found under this API key/mode: {e}",
            }
        except Exception as e:
            result["prices"][key] = {
                "configured": True,
                "visible": False,
                "message": f"Unexpected error retrieving price: {e}",
            }

    return result

# @router.post("/webhook")
# async def stripe_webhook(request: Request, stripe_signature: str = Header(None)):
#     """
#     Listens for events from Stripe. This is the most critical endpoint.
#     """
#     payload = await request.body()
#     try:
#         event = stripe.Webhook.construct_event(
#             payload=payload,
#             sig_header=stripe_signature,
#             secret=settings.STRIPE_WEBHOOK_SECRET,
#         )
#     except ValueError as e:
#         # Invalid payload
#         raise HTTPException(status_code=400, detail=str(e))
#     except stripe.error.SignatureVerificationError as e:
#         # Invalid signature
#         raise HTTPException(status_code=400, detail=str(e))

#     # Handle the event
#     event_type = event["type"]
#     print("event type is ", event_type)
#     data = event["data"]["object"]

#     logger.info(f"Received Stripe webhook event: {event_type}")

#     if event_type == "checkout.session.completed":
#         user_id = data.get("client_reference_id")
#         if not user_id:
#             return {"status": "error", "message": "User ID not found in session."}

#         subscription = stripe.Subscription.retrieve(data.get("subscription"))

#         # Update user profile in Supabase
#         supabase.table("user_profiles").update(
#             {
#                 "plan_id": subscription.plan.product,
#                 "subscription_status": subscription.status,
#                 "stripe_customer_id": data.get("customer"),
#             }
#         ).eq("user_id", user_id).execute()

#     elif event_type in [
#         "customer.subscription.updated",
#         "customer.subscription.deleted",
#     ]:
#         print("data is ",data)
#         customer_id = data.get("customer")
#         # Find user by customer_id
#         user_profile = (
#             supabase.table("user_profiles")
#             .select("user_id")
#             .eq("stripe_customer_id", customer_id)
#             .single()
#             .execute()
#         )
#         if not user_profile.data:
#             return {"status": "error", "message": "User not found for customer ID."}

#         user_id = user_profile.data["user_id"]



#         supabase.table("user_profiles").update(
#             {
#                 "plan_id": data.plan.product,
#                 "subscription_status": data.status,
#             }
#         ).eq("user_id", user_id).execute()

#     else:
#         logger.warning(f"Unhandled event type {event_type}")

#     return {"status": "success"}
